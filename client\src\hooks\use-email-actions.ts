import { useMutation, useQueryClient } from '@tanstack/react-query';
import { usePermission } from '@/hooks/use-permission';
import { useToast } from '@/hooks/use-toast';
import apiClient from '@/lib/apiClient';
import { EmailAction } from '@/lib/constants';
import type { Email } from '@/types/email';

/**
 * Custom hook for email-related actions
 * Separates API calls and state management from UI components
 */
export function useEmailActions() {
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const { handlePermissionError } = usePermission();

  /**
   * Generic error handler for email actions with permission handling
   */
  const handleEmailActionError = async (error: unknown, actionName: string) => {
    if (error instanceof Error) {
      // First check if this is a permission or token-related error
      const wasHandled = await handlePermissionError(error.message);

      if (wasHandled) {
        // The permission handler already displayed a toast and set up redirection
        return;
      }

      // If not handled as a permission error, show appropriate error toast
      toast({
        title: `Failed to ${actionName}`,
        description: error.message,
        variant: 'destructive',
      });
    } else {
      // Unknown error type
      toast({
        title: `Failed to ${actionName}`,
        description: 'An unknown error occurred',
        variant: 'destructive',
      });
    }
  };

  const createEmailActionMutation = <TData = unknown, TVariables = number>(
    endpoint: (id: TVariables) => string,
    actionName: string,
    successMessage: string
  ) => {
    return useMutation<TData, Error, TVariables>({
      mutationFn: (variables: TVariables) => apiClient.post(endpoint(variables)),
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: ['/api/emails'] });
        toast({ title: 'Success', description: successMessage });
      },
      onError: (error) => handleEmailActionError(error, actionName),
    });
  };

  const archiveMutation = createEmailActionMutation(
    (id) => `/api/emails/${id}/archive`,
    EmailAction.ARCHIVE,
    'Email archived successfully.'
  );

  const trashMutation = createEmailActionMutation(
    (id) => `/api/emails/${id}/trash`,
    EmailAction.TRASH,
    'Email moved to trash.'
  );

  const markImportantMutation = useMutation<void, Error, { id: number; important: boolean }>({
    mutationFn: ({ id, important }) =>
      apiClient.post(`/api/emails/${id}/mark-important`, { important }),
    onSuccess: (_, { important }) => {
      queryClient.invalidateQueries({ queryKey: ['/api/emails'] });
      toast({
        title: 'Success',
        description: `Email marked as ${important ? 'important' : 'not important'}.`,
      });
    },
    onError: (error) => handleEmailActionError(error, EmailAction.MARK_IMPORTANT),
  });

  const snoozeMutation = useMutation<void, Error, { emailId: number; snoozeUntil: Date | null }>({
    mutationFn: ({ emailId, snoozeUntil }) =>
      apiClient.post(`/api/emails/${emailId}/snooze`, { snoozeUntil }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/emails'] });
      toast({ title: 'Success', description: 'Email snoozed successfully.' });
    },
    onError: (error) => handleEmailActionError(error, EmailAction.SNOOZE),
  });

  const regenerateSummaryMutation = useMutation<
    string,
    Error,
    number,
    { previousEmail: Email | undefined }
  >({
    mutationFn: (emailId) => apiClient.post(`/api/ai/regenerate-summary/${emailId}`),
    onMutate: async (emailId) => {
      await queryClient.cancelQueries({ queryKey: ['/api/emails', emailId] });
      const previousEmail = queryClient.getQueryData<Email>(['/api/emails', emailId]);
      queryClient.setQueryData<Email>(['/api/emails', emailId], (old) =>
        old ? { ...old, summary: 'Processing...' } : old
      );
      return { previousEmail };
    },
    onSuccess: (newSummary, emailId) => {
      queryClient.setQueryData<Email>(['/api/emails', emailId], (old) =>
        old ? { ...old, summary: newSummary } : old
      );
      toast({ title: 'Summary regenerated', description: 'The AI summary has been updated.' });
    },
    onError: (err, emailId, context) => {
      if (context?.previousEmail) {
        queryClient.setQueryData(['/api/emails', emailId], context.previousEmail);
      }
      toast({
        title: 'Failed to regenerate summary',
        description: err.message,
        variant: 'destructive',
      });
    },
    onSettled: (_data, _error, emailId) => {
      queryClient.invalidateQueries({ queryKey: ['/api/emails', emailId] });
      queryClient.invalidateQueries({ queryKey: ['/api/emails'] });
    },
  });

  // Send reply mutation
  const sendReplyMutation = useMutation({
    mutationFn: async (replyData: { emailId: number; text: string }) => {
      return apiClient.post(`/api/emails/${replyData.emailId}/reply`, { text: replyData.text });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/emails'] });
      toast({
        title: 'Success',
        description: 'Your reply has been sent.',
      });
    },
    onError: (error) => handleEmailActionError(error, 'send reply'),
  });

  const updateEmailWithNewSummary = (
    emails: Email[],
    emailId: number,
    newSummary: string
  ): Email[] => {
    return emails.map((email) =>
      email.id === emailId ? { ...email, summary: newSummary } : email
    );
  };

  const findEmailById = (emails: Email[], emailId: number): Email | undefined => {
    return emails.find((email) => email.id === emailId);
  };

  return {
    // Archive email
    archiveEmail: archiveMutation.mutateAsync,
    isArchiving: archiveMutation.isPending,

    // Trash email
    trashEmail: trashMutation.mutateAsync,
    isTrashing: trashMutation.isPending,

    // Mark as important
    markEmailAsImportant: markImportantMutation.mutateAsync,
    isMarkingImportant: markImportantMutation.isPending,

    // Snooze email
    snoozeEmail: snoozeMutation.mutateAsync,
    isSnoozing: snoozeMutation.isPending,

    // Summary regeneration
    regenerateSummary: regenerateSummaryMutation.mutateAsync,
    isRegeneratingSummary: regenerateSummaryMutation.isPending,

    // Send reply
    sendReply: sendReplyMutation.mutate,
    isSendingReply: sendReplyMutation.isPending,

    // Helper method to update an email summary in context
    updateEmailWithNewSummary,

    // Helper method to find an email by ID
    findEmailById,
  };
}
