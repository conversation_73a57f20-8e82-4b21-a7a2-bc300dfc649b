import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { createContext, type ReactNode, useContext, useState, useCallback, useMemo, useRef } from 'react';
import { usePermission } from '@/hooks/use-permission';
import { useToast } from '@/hooks/use-toast';
import apiClient from '@/lib/apiClient';
import type { Email } from '@/types/email';
import { useAuth } from './AuthContext';

interface EmailDetailContextType {
  selectedEmail: Email | null;
  isLoading: boolean;
  error: Error | null;
  selectEmail: (emailId: number | null) => void;
  archiveSelectedEmail: () => Promise<void>;
  sendReplyToSelected: (content: string) => Promise<void>;
  regenerateReplyForSelected: (tone: string) => Promise<string>;
  regenerateSummaryForSelected: () => Promise<string>;
  explainCategoriesForSelected: () => Promise<string>;
  isArchiving: boolean;
  isSendingReply: boolean;
  isRegeneratingReply: boolean;
  isRegeneratingSummary: boolean;
  isExplainingCategories: boolean;
}

const EmailDetailContext = createContext<EmailDetailContextType | undefined>(undefined);

// Use named function components to avoid Fast Refresh incompatibility
export function EmailDetailProvider({ children }: { children: ReactNode }) {
  const [selectedEmailId, setSelectedEmailId] = useState<number | null>(null);
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const { user } = useAuth();
  // Determine if we're in test mode based on the email
  const isTestMode =
    user && (user.email === '<EMAIL>' || user.email === '<EMAIL>');
  const { handlePermissionError } = usePermission();
  
  // Use ref to track processing state instead of window pollution
  const processingEmailIds = useRef<Set<number>>(new Set());

  const updateEmailInCache = (emailId: number, newProps: Partial<Email>) => {
    const queryKey = ['emails', 'detail', emailId];
    // Optimistically update the email detail query
    queryClient.setQueryData<Email>(queryKey, (oldData) =>
      oldData ? { ...oldData, ...newProps } : undefined
    );

    // Optimistically update the email in any list queries
    queryClient
      .getQueryCache()
      .findAll({ queryKey: ['emails', user?.id] })
      .forEach((query) => {
        const previousData = queryClient.getQueryData<any>(query.queryKey);
        if (previousData?.emails) {
          queryClient.setQueryData(query.queryKey, {
            ...previousData,
            emails: previousData.emails.map((email: Email) =>
              email.id === emailId ? { ...email, ...newProps } : email
            ),
          });
        }
      });
  };

  // Fetch selected email details
  const {
    data: selectedEmail,
    isLoading,
    error,
  } = useQuery<Email | null>({
    queryKey: ['emails', 'detail', selectedEmailId],
    enabled: !!selectedEmailId && !isTestMode,
    retry: 1,
  });

  // If email doesn't exist in query cache yet, try to find it
  const getSelectedEmailFromCache = useCallback((emailId: number): Email | null => {
    if (!emailId) return null;

    // Try to get from cache first
    const cachedData = queryClient.getQueryData(['emails', user?.id]);
    if (cachedData) {
      // Cast to any to handle different data structures safely
      const cachedEmails = cachedData as any;

      // Check if it has the emails property
      if (cachedEmails.emails && Array.isArray(cachedEmails.emails)) {
        const email = cachedEmails.emails.find((e: Email) => e.id === emailId);
        if (email) return email;
      }
    }

    // If not in cache, return null (will trigger a query)
    return null;
  }, [queryClient, user?.id]);

  const { mutateAsync: archiveEmailMutation, isPending: isArchiving } = useMutation<
    void,
    Error,
    number
  >({
    mutationFn: (id) => apiClient.post(`/api/emails/${id}/archive`),
    onSuccess: () => {
      toast({
        title: 'Email archived',
        description: 'The email has been successfully archived.',
      });
      queryClient.invalidateQueries({ queryKey: ['emails', user?.id] });
      queryClient.invalidateQueries({ queryKey: ['stats'] });
    },
  });

  const { mutateAsync: sendReplyMutation, isPending: isSendingReply } = useMutation<
    void,
    Error,
    { id: number; content: string }
  >({
    mutationFn: ({ id, content }) => apiClient.post(`/api/emails/${id}/reply`, { content }),
    onSuccess: (_, { id }) => {
      toast({
        title: 'Reply sent',
        description: 'Your reply has been successfully sent.',
      });
      queryClient.invalidateQueries({ queryKey: ['emails', user?.id] });
      queryClient.invalidateQueries({ queryKey: ['stats'] });
      queryClient.invalidateQueries({ queryKey: ['emails', 'detail', id] });
    },
  });

  const { mutateAsync: regenerateReplyMutation, isPending: isRegeneratingReply } = useMutation<
    string,
    Error,
    { id: number; tone: string }
  >({
    mutationFn: ({ id, tone }) => apiClient.post(`/api/ai/regenerate-reply/${id}`, { tone }),
  });

  const { mutateAsync: regenerateSummaryMutation, isPending: isRegeneratingSummary } = useMutation<
    string,
    Error,
    number,
    { previousDetailEmail: Email | null | undefined }
  >({
    mutationFn: (id: number) => apiClient.post(`/api/ai/regenerate-summary/${id}`),
    onMutate: async (emailId) => {
      await queryClient.cancelQueries({ queryKey: ['emails', 'detail', emailId] });
      const previousDetailEmail = queryClient.getQueryData<Email>(['emails', 'detail', emailId]);
      updateEmailInCache(emailId, { summary: 'Processing...' });
      return { previousDetailEmail };
    },
    onSuccess: (newSummary, emailId) => {
      toast({
        title: 'Summary regenerated',
        description: 'The AI summary has been updated successfully.',
      });
      updateEmailInCache(emailId, { summary: newSummary });
    },
    onError: (error, emailId, context) => {
      console.error(`Error regenerating summary for email ${emailId}:`, error);
      toast({
        title: 'Summary regeneration failed',
        description: 'Failed to update the email summary. Please try again.',
        variant: 'destructive',
      });
      if (context?.previousDetailEmail) {
        queryClient.setQueryData(['emails', 'detail', emailId], context.previousDetailEmail);
      } else {
        updateEmailInCache(emailId, { summary: 'Error generating summary' });
      }
    },
  });

  const { mutateAsync: explainCategoriesMutation, isPending: isExplainingCategories } = useMutation<
    string,
    Error,
    number
  >({
    mutationFn: (id: number) => apiClient.post(`/api/ai/explain-categories/${id}`),
  });

  // Function to select an email
  const selectEmail = useCallback((emailId: number | null) => {
    setSelectedEmailId(emailId);

    // If we're in test mode or we have the email in cache, no need to fetch
    if (isTestMode || (emailId && getSelectedEmailFromCache(emailId))) {
      return;
    }

    // Otherwise, we'll let the query fetch the email
  }, [isTestMode, getSelectedEmailFromCache]);

  // Archive the selected email
  const archiveSelectedEmail = useCallback(async () => {
    if (!selectedEmailId) {
      toast({
        title: 'No email selected',
        description: 'Please select an email to archive.',
        variant: 'destructive',
      });
      return;
    }

    try {
      await archiveEmailMutation(selectedEmailId);
    } catch (error) {
      // Check if it's a permission error
      if (error instanceof Error) {
        const wasHandled = await handlePermissionError(error.message);
        if (wasHandled) {
          // If permission handling was initiated, show a message
          toast({
            title: 'Additional permissions needed',
            description: 'Please complete the permission request to archive emails.',
          });
          return;
        }
      }

      // Re-throw if not handled
      throw error;
    }
  }, [selectedEmailId, archiveEmailMutation, handlePermissionError, toast]);

  // Send a reply to the selected email
  const sendReplyToSelected = useCallback(async (content: string) => {
    if (!selectedEmailId) {
      toast({
        title: 'No email selected',
        description: 'Please select an email to reply to.',
        variant: 'destructive',
      });
      return;
    }

    try {
      await sendReplyMutation({ id: selectedEmailId, content });
    } catch (error) {
      // Check if it's a permission error
      if (error instanceof Error) {
        const wasHandled = await handlePermissionError(error.message);
        if (wasHandled) {
          // If permission handling was initiated, show a message
          toast({
            title: 'Additional permissions needed',
            description: 'Please complete the permission request to send replies.',
          });
          return;
        }
      }

      // Re-throw if not handled
      throw error;
    }
  }, [selectedEmailId, sendReplyMutation, handlePermissionError, toast]);

  // Regenerate a reply for the selected email
  const regenerateReplyForSelected = useCallback(async (tone: string): Promise<string> => {
    if (!selectedEmailId) {
      toast({
        title: 'No email selected',
        description: 'Please select an email to generate a reply for.',
        variant: 'destructive',
      });
      return '';
    }

    return await regenerateReplyMutation({ id: selectedEmailId, tone });
  }, [selectedEmailId, regenerateReplyMutation, toast]);

  // Regenerate a summary for the selected email
  const regenerateSummaryForSelected = useCallback(async (): Promise<string> => {
    if (!selectedEmailId) {
      toast({
        title: 'No email selected',
        description: 'Please select an email to regenerate the summary for.',
        variant: 'destructive',
      });
      return '';
    }

    try {
      // Use ref to prevent multiple refresh operations for the same email
      if (processingEmailIds.current.has(selectedEmailId)) {
        return '';
      }

      // Set the processing flag
      processingEmailIds.current.add(selectedEmailId);

      try {
        const newSummary = await regenerateSummaryMutation(selectedEmailId);

        // Clear the processing flag after a short delay
        setTimeout(() => {
          processingEmailIds.current.delete(selectedEmailId);
        }, 2000);

        return newSummary;
      } catch (error) {
        // Clear the processing flag in case of error
        processingEmailIds.current.delete(selectedEmailId);
        throw error;
      }
    } catch (error) {
      console.error('Error in regenerateSummaryForSelected:', error);
      throw error; // Re-throw to let the mutation error handler deal with it
    }
  }, [selectedEmailId, regenerateSummaryMutation, toast]);

  // Explain the categories for the selected email
  const explainCategoriesForSelected = useCallback(async (): Promise<string> => {
    if (!selectedEmailId) {
      toast({
        title: 'No email selected',
        description: 'Please select an email to explain categories for.',
        variant: 'destructive',
      });
      return '';
    }

    return await explainCategoriesMutation(selectedEmailId);
  }, [selectedEmailId, explainCategoriesMutation, toast]);

  // If we're in test mode, use the cached email for preview
  const effectiveSelectedEmail =
    isTestMode && selectedEmailId
      ? getSelectedEmailFromCache(selectedEmailId)
      : selectedEmail || null;

  // Memoize the context value to prevent unnecessary re-renders
  const contextValue = useMemo(
    () => ({
      selectedEmail: effectiveSelectedEmail,
      isLoading,
      error: error as Error | null,
      selectEmail,
      archiveSelectedEmail,
      sendReplyToSelected,
      regenerateReplyForSelected,
      regenerateSummaryForSelected,
      explainCategoriesForSelected,
      isArchiving,
      isSendingReply,
      isRegeneratingReply,
      isRegeneratingSummary,
      isExplainingCategories,
    }),
    [
      effectiveSelectedEmail,
      isLoading,
      error,
      selectEmail,
      archiveSelectedEmail,
      sendReplyToSelected,
      regenerateReplyForSelected,
      regenerateSummaryForSelected,
      explainCategoriesForSelected,
      isArchiving,
      isSendingReply,
      isRegeneratingReply,
      isRegeneratingSummary,
      isExplainingCategories,
    ]
  );

  return (
    <EmailDetailContext.Provider value={contextValue}>
      {children}
    </EmailDetailContext.Provider>
  );
}

// Use named function to avoid Fast Refresh incompatibility
export function useEmailDetail() {
  const context = useContext(EmailDetailContext);
  if (context === undefined) {
    throw new Error('useEmailDetail must be used within an EmailDetailProvider');
  }
  return context;
}
