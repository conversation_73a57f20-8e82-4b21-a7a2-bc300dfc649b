import { useQueryClient } from '@tanstack/react-query';
import { RefreshCw } from 'lucide-react';
import type React from 'react';
import { memo, useCallback, useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import { useEmailDetail } from '@/context/EmailDetailContext';
import { useEmailList } from '@/context/EmailListContext';
import { useEmailActions } from '@/hooks/use-email-actions';
import { useToast } from '@/hooks/use-toast';
import { useIsMobile } from '@/hooks/use-mobile';
import { useAuth } from '@/context/AuthContext';
import type { Email } from '@/types/email';

interface RegenerateSummaryButtonProps {
  emailId: number;
}

/**
 * Button component to regenerate the AI summary for an email
 * - Uses optimized cache update strategy
 * - Provides visual feedback during processing
 * - Handles errors gracefully
 */
const RegenerateSummaryButton: React.FC<RegenerateSummaryButtonProps> = ({ emailId }) => {
  const { emails, refreshEmails } = useEmailList();
  const { selectedEmail, selectEmail } = useEmailDetail();
  const { regenerateSummary } = useEmailActions();
  const { toast } = useToast();
  const { user } = useAuth();
  const isMobile = useIsMobile();
  const queryClient = useQueryClient();

  interface PaginatedEmails {
    emails: Email[];
  }

  // Track component-specific loading state separate from global state
  const [isLocallyProcessing, setIsLocallyProcessing] = useState(false);

  // Find the current email
  const currentEmail = emails.find((email: Email) => email.id === emailId) || selectedEmail;

  // Memoize the query update function to avoid recreation on every render
  const updateQueryCache = useCallback(
    (newSummary: string) => {
      // Get all relevant query keys that might contain this email
      const queryCache = queryClient.getQueryCache();
      const emailQueries = queryCache.findAll({
        queryKey: ['emails', user?.id],
        exact: false,
      });

      // Update detail view cache
      queryClient.setQueryData<Email | undefined>(['emails', 'detail', emailId], (oldData) => {
        if (!oldData) return oldData;
        return {
          ...oldData,
          summary: newSummary,
        };
      });

      // Update all email list caches
      for (const query of emailQueries) {
        const queryKey = query.queryKey;
        const data = queryClient.getQueryData<PaginatedEmails | Email[]>(queryKey);

        if (!data) continue;

        if (Array.isArray(data)) {
          const updatedEmails = data.map((email) =>
            email.id === emailId ? { ...email, summary: newSummary } : email
          );
          queryClient.setQueryData(queryKey, updatedEmails);
        } else {
          const updatedEmails = data.emails.map((email) =>
            email.id === emailId ? { ...email, summary: newSummary } : email
          );
          queryClient.setQueryData(queryKey, {
            ...data,
            emails: updatedEmails,
          });
        }
      }
    },
    [emailId, queryClient, user?.id]
  );

  // Memoized regeneration handler to prevent unnecessary recreations
  const handleRegenerateSummary = useCallback(async () => {
    // Don't allow multiple simultaneous regenerations
    if (isLocallyProcessing) return;

    try {
      setIsLocallyProcessing(true);

      // Update UI immediately to show processing state
      updateQueryCache('Processing...');

      // Call the API to regenerate the summary
      const newSummary = await regenerateSummary(emailId);

      // Allow a moment for backend processing to complete
      await new Promise((resolve) => setTimeout(resolve, 500));

      // Update all caches with the new summary
      updateQueryCache(newSummary);

      // Force a refresh to ensure we have the latest data from server
      await refreshEmails();

      // Re-select the current email to ensure we're showing the latest version
      selectEmail(emailId);

      toast({
        title: 'Summary Updated',
        description: 'Email summary has been refreshed with latest data.',
        variant: 'default',
      });
    } catch (error) {
      console.error('Error regenerating summary:', error);

      // Update cache to show error state
      updateQueryCache('Error generating summary');

      toast({
        title: 'Summary Update Failed',
        description: 'There was a problem refreshing the summary. Please try again.',
        variant: 'destructive',
      });

      // Refresh emails to get the current state from server
      refreshEmails();
    } finally {
      setIsLocallyProcessing(false);
    }
  }, [
    emailId,
    isLocallyProcessing,
    regenerateSummary,
    refreshEmails,
    selectEmail,
    toast,
    updateQueryCache,
  ]);

  // Combined loading state from both local and global sources
  const isLoading = isLocallyProcessing;

  return (
    <Button
      variant="ghost"
      size="icon"
      className={`${isMobile ? 'h-5 w-5' : 'h-6 w-6'} hover:bg-primary/10 transition-colors relative group`}
      onClick={handleRegenerateSummary}
      disabled={isLoading}
      title="Regenerate summary"
    >
      <RefreshCw
        className={`
          ${isMobile ? 'h-3 w-3' : 'h-4 w-4'} 
          ${isLoading ? 'animate-spin text-primary' : 'group-hover:scale-110 transition-transform'}
        `}
      />
      <span className="sr-only">Regenerate summary</span>
      <span className="absolute -bottom-8 right-0 bg-primary text-primary-foreground text-[10px] sm:text-xs px-1.5 sm:px-2 py-0.5 sm:py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap pointer-events-none z-20">
        Regenerate summary
      </span>
    </Button>
  );
};

// Use memo to prevent unnecessary re-renders
export default memo(RegenerateSummaryButton);
