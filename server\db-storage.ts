import {
  type Achievement,
  achievements,
  type Email,
  emails,
  type InsertAchievement,
  type InsertEmail,
  type InsertSettings,
  type InsertUser,
  type Settings,
  settings,
  type User,
  users,
} from '@shared/schema';
import { and, count, desc, eq, inArray, isNull, not, type SQL, sql, or } from 'drizzle-orm';
import type { Credentials } from 'google-auth-library';
import { getDb } from './db';
import logger from './lib/logger';
import type { IStorage } from './storage';
import { monitoredQuery } from './utils/queryMonitor';
import { normalizeEmailArrayFields, normalizeEmailsArrayFields } from './lib/arrayFieldFix';

// Phase 2: Query Optimization - Bulk Operations Interface
interface BulkEmailUpdate {
  id: number;
  updates: Partial<Email>;
}

interface BulkEmailResult {
  successful: number;
  failed: number;
  errors: Array<{ id: number; error: string }>;
}

interface QueryCacheEntry {
  data: any;
  timestamp: number;
  ttl: number;
}

interface QueryMetrics {
  totalQueries: number;
  cachedQueries: number;
  bulkOperations: number;
  optimizedQueries: number;
  averageQueryTime: number;
  slowQueries: Array<{ query: string; duration: number; timestamp: number }>;
  // Phase 4: Advanced metrics
  streamedQueries: number;
  compressedResponses: number;
  cacheWarmingOperations: number;
  compressionRatio: number;
}

// Phase 4: Streaming interface for large datasets
interface StreamingOptions {
  chunkSize: number;
  enableCompression: boolean;
  compressionThreshold: number; // Minimum size in bytes to trigger compression
}

// Phase 4: Compression metrics
interface CompressionResult {
  originalSize: number;
  compressedSize: number;
  compressionRatio: number;
  compressionTime: number;
}

// Phase 4: Cache warming configuration
interface CacheWarmingConfig {
  enabled: boolean;
  warmingQueries: string[];
  warmingInterval: number; // milliseconds
  maxWarmingTime: number; // maximum time to spend warming cache
}

export class DbStorage implements IStorage {
  // Phase 2: Query cache for frequently accessed data
  private queryCache = new Map<string, QueryCacheEntry>();
  private readonly CACHE_TTL = 30000; // 30 seconds
  private readonly MAX_CACHE_SIZE = 1000;
  
  // Phase 2: Query metrics tracking
  private queryMetrics: QueryMetrics = {
    totalQueries: 0,
    cachedQueries: 0,
    bulkOperations: 0,
    optimizedQueries: 0,
    averageQueryTime: 0,
    slowQueries: [],
    // Phase 4: Advanced metrics
    streamedQueries: 0,
    compressedResponses: 0,
    cacheWarmingOperations: 0,
    compressionRatio: 0,
  };

  // Phase 4: Advanced optimization settings
  private streamingOptions: StreamingOptions = {
    chunkSize: 100, // Process 100 records at a time
    enableCompression: true,
    compressionThreshold: 1024 * 10, // 10KB threshold for compression
  };

  private cacheWarmingConfig: CacheWarmingConfig = {
    enabled: true,
    warmingQueries: [
      'getEmails:recent',
      'getEmailCount:unread',
      'getSettings:user',
    ],
    warmingInterval: 300000, // 5 minutes
    maxWarmingTime: 30000, // 30 seconds max for warming
  };

  private cacheWarmingTimer: NodeJS.Timeout | null = null;

  // Core operations
  async testConnection(): Promise<boolean> {
    try {
      const db = await getDb();
      if (!db) {
        logger.warn('[DbStorage] Database connection is not available for test');
        return false;
      }
      type DbRow = Record<string, unknown>;
      const result = (await db.execute(sql`SELECT 1 as connected`)) as unknown as DbRow[];

      return Array.isArray(result) && result.length > 0;
    } catch (error) {
      logger.error('[DbStorage] Database connection test failed:', error);
      return false;
    }
  }

  /**
   * Phase 2: Get query cache key for caching frequently accessed data
   */
  private getCacheKey(operation: string, params: any[]): string {
    return `${operation}:${JSON.stringify(params)}`;
  }

  /**
   * Phase 2: Check if cache entry is valid
   */
  private isCacheValid(entry: QueryCacheEntry): boolean {
    return Date.now() - entry.timestamp < entry.ttl;
  }

  /**
   * Phase 2: Get data from cache if available and valid
   */
  private getFromCache<T>(key: string): T | null {
    const entry = this.queryCache.get(key);
    if (entry && this.isCacheValid(entry)) {
      this.queryMetrics.cachedQueries++;
      return entry.data as T;
    }
    return null;
  }

  /**
   * Phase 2: Store data in cache with TTL
   */
  private setCache(key: string, data: any, ttl: number = this.CACHE_TTL): void {
    // Prevent cache from growing too large
    if (this.queryCache.size >= this.MAX_CACHE_SIZE) {
      // Remove oldest entries
      const entries = Array.from(this.queryCache.entries());
      entries.sort((a, b) => a[1].timestamp - b[1].timestamp);
      const toRemove = entries.slice(0, Math.floor(this.MAX_CACHE_SIZE * 0.2));
      toRemove.forEach(([key]) => this.queryCache.delete(key));
    }

    this.queryCache.set(key, {
      data,
      timestamp: Date.now(),
      ttl,
    });
  }

  /**
   * Phase 2: Clear cache entries matching pattern
   */
  private clearCachePattern(pattern: string): void {
    const keysToDelete = Array.from(this.queryCache.keys()).filter(key => 
      key.includes(pattern)
    );
    keysToDelete.forEach(key => this.queryCache.delete(key));
  }

  /**
   * Phase 2: Track query performance
   */
  private trackQueryPerformance(queryName: string, duration: number): void {
    this.queryMetrics.totalQueries++;
    
    // Update average query time
    const totalTime = this.queryMetrics.averageQueryTime * (this.queryMetrics.totalQueries - 1);
    this.queryMetrics.averageQueryTime = (totalTime + duration) / this.queryMetrics.totalQueries;
    
    // Track slow queries (> 1000ms)
    if (duration > 1000) {
      this.queryMetrics.slowQueries.push({
        query: queryName,
        duration,
        timestamp: Date.now(),
      });
      
      // Keep only last 50 slow queries
      if (this.queryMetrics.slowQueries.length > 50) {
        this.queryMetrics.slowQueries = this.queryMetrics.slowQueries.slice(-50);
      }
      
      logger.warn(`[DbStorage] Slow query detected: ${queryName} took ${duration}ms`);
    }
  }

  /**
   * Phase 2: Get query performance metrics
   */
  getQueryMetrics(): QueryMetrics {
    return { ...this.queryMetrics };
  }

  /**
   * Phase 2: Reset query metrics
   */
  resetQueryMetrics(): void {
    this.queryMetrics = {
      totalQueries: 0,
      cachedQueries: 0,
      bulkOperations: 0,
      optimizedQueries: 0,
      averageQueryTime: 0,
      slowQueries: [],
      // Phase 4: Advanced metrics
      streamedQueries: 0,
      compressedResponses: 0,
      cacheWarmingOperations: 0,
      compressionRatio: 0,
    };
    logger.info('[DbStorage] Query metrics reset');
  }

  /**
   * Phase 4: Compress data if it exceeds threshold
   */
  private async compressData(data: any): Promise<CompressionResult | null> {
    if (!this.streamingOptions.enableCompression) {
      return null;
    }

    const startTime = Date.now();
    const originalData = JSON.stringify(data);
    const originalSize = Buffer.byteLength(originalData, 'utf8');

    if (originalSize < this.streamingOptions.compressionThreshold) {
      return null; // Don't compress small data
    }

    try {
      // Use Node.js built-in zlib for compression
      const zlib = require('zlib');
      const compressed = zlib.gzipSync(originalData);
      const compressedSize = compressed.length;
      const compressionTime = Date.now() - startTime;
      const compressionRatio = (originalSize - compressedSize) / originalSize;

      // Update metrics
      this.queryMetrics.compressedResponses++;
      this.queryMetrics.compressionRatio = 
        (this.queryMetrics.compressionRatio + compressionRatio) / 2;

      logger.debug(`[DbStorage] Compressed data: ${originalSize} -> ${compressedSize} bytes (${(compressionRatio * 100).toFixed(1)}% reduction)`);

      return {
        originalSize,
        compressedSize,
        compressionRatio,
        compressionTime,
      };
    } catch (error) {
      logger.warn('[DbStorage] Compression failed:', error);
      return null;
    }
  }

  /**
   * Phase 4: Stream large datasets in chunks
   */
  async *streamEmails(
    userId: number,
    filters?: { archived?: boolean; important?: boolean; snoozed?: boolean; trashed?: boolean }
  ): AsyncGenerator<Email[], void, unknown> {
    const chunkSize = this.streamingOptions.chunkSize;
    let offset = 0;
    let hasMore = true;

    this.queryMetrics.streamedQueries++;

    logger.debug(`[DbStorage] Starting email stream for user ${userId} with chunk size ${chunkSize}`);

    while (hasMore) {
      try {
        const chunk = await this.getEmails(userId, chunkSize, offset, filters);
        
        if (chunk.length === 0) {
          hasMore = false;
        } else {
          yield chunk;
          offset += chunkSize;
          
          // If we got fewer results than chunk size, we've reached the end
          if (chunk.length < chunkSize) {
            hasMore = false;
          }
        }
      } catch (error) {
        logger.error(`[DbStorage] Error streaming emails at offset ${offset}:`, error);
        throw error;
      }
    }

    logger.debug(`[DbStorage] Email stream completed for user ${userId}, processed ${offset} records`);
  }

  /**
   * Phase 4: Get emails with streaming and compression support
   */
  async getEmailsOptimized(
    userId: number,
    limit = 50,
    offset = 0,
    filters?: { archived?: boolean; important?: boolean; snoozed?: boolean; trashed?: boolean },
    options?: { enableStreaming?: boolean; enableCompression?: boolean }
  ): Promise<{ emails: Email[]; compressionResult?: CompressionResult | null }> {
    const enableStreaming = options?.enableStreaming && limit > this.streamingOptions.chunkSize;
    const enableCompression = options?.enableCompression ?? this.streamingOptions.enableCompression;

    if (enableStreaming) {
      // Use streaming for large datasets
      const allEmails: Email[] = [];
      const stream = this.streamEmails(userId, filters);
      
      let processedCount = 0;
      for await (const chunk of stream) {
        allEmails.push(...chunk);
        processedCount += chunk.length;
        
        // Respect the limit
        if (processedCount >= limit) {
          break;
        }
      }

      const emails = allEmails.slice(offset, offset + limit);
      const compressionResult = enableCompression ? await this.compressData(emails) : null;

      return { emails, compressionResult };
    } else {
      // Use regular query
      const emails = await this.getEmails(userId, limit, offset, filters);
      const compressionResult = enableCompression ? await this.compressData(emails) : null;

      return { emails, compressionResult };
    }
  }

  /**
   * Phase 4: Warm cache with frequently accessed data
   */
  private async warmCache(): Promise<void> {
    if (!this.cacheWarmingConfig.enabled) {
      return;
    }

    const startTime = Date.now();
    this.queryMetrics.cacheWarmingOperations++;

    logger.debug('[DbStorage] Starting cache warming operation');

    try {
      // Get a sample of users for cache warming
      const db = await getDb();
      if (!db) {
        logger.warn('[DbStorage] Database not available for cache warming');
        return;
      }

      const sampleUsers = await db.select({ id: users.id })
        .from(users)
        .limit(10); // Warm cache for first 10 users

      for (const user of sampleUsers) {
        if (Date.now() - startTime > this.cacheWarmingConfig.maxWarmingTime) {
          logger.debug('[DbStorage] Cache warming time limit reached');
          break;
        }

        try {
          // Warm frequently accessed queries
          await this.getEmails(user.id, 20, 0); // Recent emails
          await this.getEmailCount(user.id, { archived: false }); // Unread count
          await this.getSettings(user.id); // User settings
        } catch (error) {
          logger.debug(`[DbStorage] Cache warming failed for user ${user.id}:`, error);
          // Continue with other users
        }
      }

      const warmingTime = Date.now() - startTime;
      logger.debug(`[DbStorage] Cache warming completed in ${warmingTime}ms`);
    } catch (error) {
      logger.warn('[DbStorage] Cache warming operation failed:', error);
    }
  }

  /**
   * Phase 4: Start cache warming timer
   */
  startCacheWarming(): void {
    if (this.cacheWarmingTimer) {
      clearInterval(this.cacheWarmingTimer);
    }

    if (this.cacheWarmingConfig.enabled) {
      this.cacheWarmingTimer = setInterval(() => {
        this.warmCache();
      }, this.cacheWarmingConfig.warmingInterval);

      // Perform initial cache warming
      setTimeout(() => this.warmCache(), 5000); // Wait 5 seconds after startup

      logger.info(`[DbStorage] Cache warming started with ${this.cacheWarmingConfig.warmingInterval}ms interval`);
    }
  }

  /**
   * Phase 4: Stop cache warming timer
   */
  stopCacheWarming(): void {
    if (this.cacheWarmingTimer) {
      clearInterval(this.cacheWarmingTimer);
      this.cacheWarmingTimer = null;
      logger.info('[DbStorage] Cache warming stopped');
    }
  }

  /**
   * Phase 4: Get advanced performance metrics
   */
  getAdvancedMetrics(): {
    queryMetrics: QueryMetrics;
    streamingOptions: StreamingOptions;
    cacheWarmingConfig: CacheWarmingConfig;
    cacheStats: {
      size: number;
      hitRate: number;
      memoryUsage: string;
    };
  } {
    const totalQueries = this.queryMetrics.totalQueries;
    const hitRate = totalQueries > 0 ? this.queryMetrics.cachedQueries / totalQueries : 0;
    
    // Estimate cache memory usage
    let cacheMemoryUsage = 0;
    for (const [key, entry] of this.queryCache.entries()) {
      cacheMemoryUsage += Buffer.byteLength(JSON.stringify(entry), 'utf8');
    }

    return {
      queryMetrics: { ...this.queryMetrics },
      streamingOptions: { ...this.streamingOptions },
      cacheWarmingConfig: { ...this.cacheWarmingConfig },
      cacheStats: {
        size: this.queryCache.size,
        hitRate,
        memoryUsage: `${Math.round(cacheMemoryUsage / 1024)}KB`,
      },
    };
  }

  /**
   * Phase 4: Update streaming and compression settings
   */
  updateOptimizationSettings(settings: {
    streamingOptions?: Partial<StreamingOptions>;
    cacheWarmingConfig?: Partial<CacheWarmingConfig>;
  }): void {
    if (settings.streamingOptions) {
      this.streamingOptions = { ...this.streamingOptions, ...settings.streamingOptions };
      logger.info('[DbStorage] Streaming options updated:', this.streamingOptions);
    }

    if (settings.cacheWarmingConfig) {
      const wasEnabled = this.cacheWarmingConfig.enabled;
      this.cacheWarmingConfig = { ...this.cacheWarmingConfig, ...settings.cacheWarmingConfig };
      
      // Restart cache warming if settings changed
      if (wasEnabled !== this.cacheWarmingConfig.enabled) {
        if (this.cacheWarmingConfig.enabled) {
          this.startCacheWarming();
        } else {
          this.stopCacheWarming();
        }
      }
      
      logger.info('[DbStorage] Cache warming config updated:', this.cacheWarmingConfig);
    }
  }

  // User operations
  async getUser(id: number): Promise<User | undefined> {
    return monitoredQuery(
      'getUser',
      `SELECT * FROM users WHERE id = ${id}`,
      async () => {
        const db = await getDb();
        if (!db) {
          throw new Error('Database connection is not available');
        }
        const result = await db.select().from(users).where(eq(users.id, id));
        return result[0];
      },
      { params: [id] }
    );
  }

  // Alias for getUser to match updated service logic
  async getUserById(id: number): Promise<User | undefined> {
    return this.getUser(id);
  }

  async getUserByEmail(email: string): Promise<User | undefined> {
    try {
      const db = await getDb();
      if (!db) {
        throw new Error('Database connection is not available');
      }
      const result = await db.select().from(users).where(eq(users.email, email));
      return result[0];
    } catch (error) {
      logger.error('[DbStorage] Failed to get user by email:', { error, email });
      throw error;
    }
  }

  async getUsersByEmail(email: string): Promise<User[]> {
    try {
      const db = await getDb();
      if (!db) {
        throw new Error('Database connection is not available');
      }
      const result = await db.select().from(users).where(eq(users.email, email));
      return result;
    } catch (error) {
      logger.error('[DbStorage] Failed to get users by email:', { error, email });
      throw error;
    }
  }

  async getUserByFirebaseUid(firebaseUid: string): Promise<User | undefined> {
    try {
      const db = await getDb();
      if (!db) {
        throw new Error('Database connection is not available');
      }
      const result = await db.select().from(users).where(eq(users.firebaseUid, firebaseUid));
      return result[0];
    } catch (error) {
      logger.error('[DbStorage] Failed to get user by Firebase UID:', { error, firebaseUid });
      throw error;
    }
  }

  async createUser(user: InsertUser): Promise<User> {
    try {
      const db = await getDb();
      if (!db) {
        throw new Error('Database connection is not available');
      }
      const result = await db.insert(users).values(user).returning();
      return result[0];
    } catch (error) {
      logger.error('[DbStorage] Failed to create user:', { error, userEmail: user.email });
      throw error;
    }
  }

  async updateUser(id: number, userData: Partial<User>): Promise<User | undefined> {
    try {
      const db = await getDb();
      if (!db) {
        throw new Error('Database connection is not available');
      }
      const result = await db.update(users).set(userData).where(eq(users.id, id)).returning();

      if (!result[0]) return undefined;

      return result[0];
    } catch (error) {
      logger.error(`[DbStorage] Error updating user ${id}:`, error);
      throw error;
    }
  }

  async updateUserTokens(id: number, tokens: Credentials): Promise<User | undefined> {
    try {
      const db = await getDb();
      if (!db) {
        throw new Error('Database connection is not available');
      }
      const result = await db
        .update(users)
        .set({ gmailTokens: JSON.stringify(tokens) })
        .where(eq(users.id, id))
        .returning();

      if (!result[0]) return undefined;

      return result[0];
    } catch (error) {
      logger.error(`[DbStorage] Error updating user tokens for user ${id}:`, error);
      throw error;
    }
  }

  async getAllUsers(): Promise<User[]> {
    try {
      const db = await getDb();
      if (!db) {
        throw new Error('Database connection is not available');
      }
      const result = await db.select().from(users);
      return result;
    } catch (error) {
      logger.error('[DbStorage] Error getting all users:', error);
      return [];
    }
  }

  async deleteUser(id: number): Promise<boolean> {
    try {
      const db = await getDb();
      if (!db) {
        throw new Error('Database connection is not available');
      }
      const result = await db.delete(users).where(eq(users.id, id)).returning({ id: users.id });
      return result.length > 0;
    } catch (error) {
      logger.error(`[DbStorage] Error deleting user ${id}:`, error);
      throw error;
    }
  }

  // Email operations
  async getEmails(
    userId: number,
    limit = 50,
    offset = 0,
    filters?: { archived?: boolean; important?: boolean; snoozed?: boolean; trashed?: boolean }
  ): Promise<Email[]> {
    const startTime = Date.now();
    
    // Phase 2: Check cache for frequently accessed queries
    const cacheKey = this.getCacheKey('getEmails', [userId, limit, offset, filters]);
    const cachedResult = this.getFromCache<Email[]>(cacheKey);
    if (cachedResult) {
      this.trackQueryPerformance('getEmails_cached', Date.now() - startTime);
      return cachedResult;
    }

    return monitoredQuery(
      'getEmails',
      `SELECT * FROM emails WHERE user_id = ${userId} WITH FILTERS`,
      async () => {
        const db = await getDb();
        if (!db) {
          throw new Error('Database connection is not available');
        }
        
        // Phase 2: Optimized query building
        let whereCondition: SQL<unknown> = eq(emails.userId, userId);

        // Handle filters with optimized conditions
        if (filters) {
          // Trashed filter - emails moved to trash
          if (filters.trashed === true) {
            // Trash view: only show trashed emails
            whereCondition = and(whereCondition, eq(emails.isTrashed, true)) as SQL<unknown>;
          } else {
            // For all other views: exclude trashed emails by default
            whereCondition = and(whereCondition, eq(emails.isTrashed, false)) as SQL<unknown>;
          }

          // Archive filter
          if (filters.archived === true) {
            // Archive view: only show archived emails
            whereCondition = and(whereCondition, eq(emails.isArchived, true)) as SQL<unknown>;
          } else if (!filters.important && !filters.snoozed && !filters.trashed) {
            // Default inbox view: only show non-archived emails
            whereCondition = and(whereCondition, eq(emails.isArchived, false)) as SQL<unknown>;
          }

          // Important filter - emails flagged as important
          if (filters.important === true) {
            whereCondition = and(whereCondition, eq(emails.isImportant, true)) as SQL<unknown>;
          }

          // Snoozed filter - emails with a snoozedUntil date in the future
          if (filters.snoozed === true) {
            whereCondition = and(whereCondition, not(isNull(emails.snoozedUntil))) as SQL<unknown>;
          }
        } else {
          // Default behavior (inbox): show only non-archived and non-trashed emails
          whereCondition = and(
            whereCondition,
            eq(emails.isArchived, false),
            eq(emails.isTrashed, false)
          ) as SQL<unknown>;
        }

        // Phase 2: Optimized query execution with selective fields for better performance
        const results = await db
          .select({
            id: emails.id,
            provider: emails.provider,
            userId: emails.userId,
            messageId: emails.messageId,
            threadId: emails.threadId,
            subject: emails.subject,
            snippet: emails.snippet,
            sender: emails.sender,
            senderEmail: emails.senderEmail,
            receivedAt: emails.receivedAt,
            isRead: emails.isRead,
            isArchived: emails.isArchived,
            isTrashed: emails.isTrashed,
            isImportant: emails.isImportant,
            categories: emails.categories,
            priority: emails.priority,
            summary: emails.summary,
            aiReply: emails.aiReply,
            // Only include content fields when specifically needed
            originalContent: limit <= 20 ? emails.originalContent : sql`NULL`,
            htmlContent: limit <= 20 ? emails.htmlContent : sql`NULL`,
            labelIds: emails.labelIds,
            snoozedUntil: emails.snoozedUntil,
            isReplied: emails.isReplied,
            replyDate: emails.replyDate,
            replyId: emails.replyId,
            // createdAt: emails.createdAt, // Not available in current schema
            // updatedAt: emails.updatedAt, // Not available in current schema
            isContentEncrypted: emails.isContentEncrypted,
            contentExpiresAt: emails.contentExpiresAt,
            retentionDays: emails.retentionDays,
          })
          .from(emails)
          .where(whereCondition)
          .orderBy(desc(emails.receivedAt)) // Order by receivedAt in descending order (newest first)
          .limit(limit)
          .offset(offset);

        // Handle null/undefined array fields that can occur with RLS
        const normalizedResults = normalizeEmailsArrayFields(results as Email[]);
        
        // Phase 2: Cache results for frequently accessed queries (small result sets)
        if (limit <= 50 && offset === 0) {
          this.setCache(cacheKey, normalizedResults, this.CACHE_TTL);
        }
        
        this.queryMetrics.optimizedQueries++;
        this.trackQueryPerformance('getEmails', Date.now() - startTime);
        
        return normalizedResults;
      },
      {
        params: [userId, limit, offset],
        userId,
      }
    );
  }

  async getEmail(id: number): Promise<Email | undefined> {
    try {
      const db = await getDb();
      if (!db) {
        throw new Error('Database connection is not available');
      }
      const result = await db.select().from(emails).where(eq(emails.id, id));
      const email = result[0];
      if (!email) return undefined;
      
      // Handle null/undefined array fields that can occur with RLS
      return normalizeEmailArrayFields(email);
    } catch (error) {
      logger.error('[DbStorage] Failed to get email:', { error, emailId: id });
      throw error;
    }
  }

  async getEmailByMessageId(userId: number, messageId: string): Promise<Email | undefined> {
    try {
      const db = await getDb();
      if (!db) {
        throw new Error('Database connection is not available');
      }
      const result = await db
        .select()
        .from(emails)
        .where(and(eq(emails.userId, userId), eq(emails.messageId, messageId)));
      const email = result[0];
      if (!email) return undefined;
      
      // Handle null/undefined array fields that can occur with RLS
      return normalizeEmailArrayFields(email);
    } catch (error) {
      logger.error('[DbStorage] Failed to get email by message ID:', { error, userId, messageId });
      throw error;
    }
  }

  async getEmailsByIds(ids: number[], userId: number): Promise<Email[]> {
    if (ids.length === 0) return [];
    
    const startTime = Date.now();

    try {
      const db = await getDb();
      if (!db) {
        throw new Error('Database connection is not available');
      }
      
      // Phase 2: Optimized batch query with chunking for large ID lists
      const CHUNK_SIZE = 100; // PostgreSQL has limits on IN clause size
      const results: Email[] = [];
      
      for (let i = 0; i < ids.length; i += CHUNK_SIZE) {
        const chunk = ids.slice(i, i + CHUNK_SIZE);
        const chunkResult = await db
          .select()
          .from(emails)
          .where(and(eq(emails.userId, userId), inArray(emails.id, chunk)));
        
        results.push(...chunkResult);
      }
      
      this.queryMetrics.optimizedQueries++;
      this.trackQueryPerformance('getEmailsByIds', Date.now() - startTime);
      
      // Handle null/undefined array fields that can occur with RLS
      return normalizeEmailsArrayFields(results);
    } catch (error) {
      logger.error('[DbStorage] Failed to get emails by IDs:', { error, userId, emailIds: ids });
      throw error;
    }
  }

  /**
   * Phase 2: Bulk update emails for improved performance
   */
  async bulkUpdateEmails(updates: BulkEmailUpdate[], userId: number): Promise<BulkEmailResult> {
    const startTime = Date.now();
    const result: BulkEmailResult = {
      successful: 0,
      failed: 0,
      errors: [],
    };

    if (updates.length === 0) return result;

    try {
      const db = await getDb();
      if (!db) {
        throw new Error('Database connection is not available');
      }

      // Process updates in batches to avoid overwhelming the database
      const BATCH_SIZE = 50;
      const batches: BulkEmailUpdate[][] = [];
      
      for (let i = 0; i < updates.length; i += BATCH_SIZE) {
        batches.push(updates.slice(i, i + BATCH_SIZE));
      }

      for (const batch of batches) {
        try {
          // Use transaction for batch consistency
          await db.transaction(async (tx) => {
            for (const update of batch) {
              try {
                // Verify ownership before updating
                const email = await tx
                  .select({ userId: emails.userId })
                  .from(emails)
                  .where(eq(emails.id, update.id))
                  .limit(1);

                if (email.length === 0 || email[0].userId !== userId) {
                  result.errors.push({
                    id: update.id,
                    error: 'Email not found or access denied',
                  });
                  result.failed++;
                  continue;
                }

                await tx
                  .update(emails)
                  .set({
                    ...update.updates,
                    // updatedAt: new Date(), // Not available in current schema
                  })
                  .where(eq(emails.id, update.id));

                result.successful++;
              } catch (error) {
                result.errors.push({
                  id: update.id,
                  error: error instanceof Error ? error.message : String(error),
                });
                result.failed++;
              }
            }
          });
        } catch (batchError) {
          // If entire batch fails, mark all as failed
          batch.forEach(update => {
            result.errors.push({
              id: update.id,
              error: batchError instanceof Error ? batchError.message : String(batchError),
            });
            result.failed++;
          });
        }
      }

      // Clear relevant cache entries
      this.clearCachePattern(`getEmails:${userId}`);
      this.clearCachePattern(`getEmailCount:${userId}`);

      this.queryMetrics.bulkOperations++;
      this.trackQueryPerformance('bulkUpdateEmails', Date.now() - startTime);

      logger.info(`[DbStorage] Bulk update completed: ${result.successful} successful, ${result.failed} failed`, {
        userId,
        totalUpdates: updates.length,
        successful: result.successful,
        failed: result.failed,
      });

      return result;
    } catch (error) {
      logger.error('[DbStorage] Bulk update failed:', { error, userId, updateCount: updates.length });
      throw error;
    }
  }

  /**
   * Phase 2: Bulk create emails for improved performance during sync
   */
  async bulkCreateEmails(emailsToCreate: InsertEmail[]): Promise<Email[]> {
    const startTime = Date.now();
    
    if (emailsToCreate.length === 0) return [];

    try {
      const db = await getDb();
      if (!db) {
        throw new Error('Database connection is not available');
      }

      // Process in batches to avoid overwhelming the database
      const BATCH_SIZE = 25;
      const results: Email[] = [];

      for (let i = 0; i < emailsToCreate.length; i += BATCH_SIZE) {
        const batch = emailsToCreate.slice(i, i + BATCH_SIZE);
        
        // Normalize array fields for each email in the batch
        const normalizedBatch = batch.map(email => ({
          ...email,
          categories: typeof email.categories === 'string' 
            ? [email.categories] 
            : Array.isArray(email.categories) 
              ? email.categories 
              : [],
          labelIds: typeof email.labelIds === 'string'
            ? [email.labelIds]
            : Array.isArray(email.labelIds)
              ? email.labelIds
              : [],
        }));

        const batchResult = await db
          .insert(emails)
          .values(normalizedBatch)
          .returning();

        results.push(...batchResult.map(normalizeEmailArrayFields));
      }

      // Clear relevant cache entries for all affected users
      const userIds = [...new Set(emailsToCreate.map(email => email.userId))];
      userIds.forEach(userId => {
        this.clearCachePattern(`getEmails:${userId}`);
        this.clearCachePattern(`getEmailCount:${userId}`);
      });

      this.queryMetrics.bulkOperations++;
      this.trackQueryPerformance('bulkCreateEmails', Date.now() - startTime);

      logger.info(`[DbStorage] Bulk create completed: ${results.length} emails created`, {
        emailCount: results.length,
        batchCount: Math.ceil(emailsToCreate.length / BATCH_SIZE),
      });

      return results;
    } catch (error) {
      logger.error('[DbStorage] Bulk create failed:', { error, emailCount: emailsToCreate.length });
      throw error;
    }
  }

  async createEmail(email: InsertEmail): Promise<Email> {
    try {
      const db = await getDb();
      if (!db) {
        throw new Error('Database connection is not available');
      }

      const { categories, labelIds, ...restOfEmail } = email;
      const emailToInsert = {
        ...restOfEmail,
        categories:
          typeof categories === 'string' ? [categories] : Array.isArray(categories) ? categories : [],
        labelIds: typeof labelIds === 'string' ? [labelIds] : Array.isArray(labelIds) ? labelIds : [],
      };

      const result = await db.insert(emails).values(emailToInsert).returning();
      return normalizeEmailArrayFields(result[0]);
    } catch (error) {
      logger.error('[DbStorage] Failed to create email:', { error, emailSubject: email.subject });
      throw error;
    }
  }

  async updateEmail(id: number, emailData: Partial<Email>): Promise<Email | undefined> {
    const startTime = Date.now();
    
    try {
      const db = await getDb();
      if (!db) {
        throw new Error('Database connection is not available');
      }
      
      const result = await db.update(emails).set({
        ...emailData,
        // updatedAt: new Date(), // Not available in current schema
      }).where(eq(emails.id, id)).returning();
      
      const email = result[0];
      if (!email) return undefined;
      
      // Phase 2: Clear relevant cache entries when email is updated
      this.clearCachePattern(`getEmails:${email.userId}`);
      this.clearCachePattern(`getEmailCount:${email.userId}`);
      
      this.trackQueryPerformance('updateEmail', Date.now() - startTime);
      
      // Handle null/undefined array fields that can occur with RLS
      return normalizeEmailArrayFields(email);
    } catch (error) {
      logger.error('[DbStorage] Failed to update email:', { error, emailId: id });
      throw error;
    }
  }

  async markEmailAsRead(id: number): Promise<boolean> {
    try {
      const db = await getDb();
      if (!db) {
        throw new Error('Database connection is not available');
      }
      const result = await db
        .update(emails)
        .set({ isRead: true })
        .where(eq(emails.id, id))
        .returning({ id: emails.id });
      return result.length > 0;
    } catch (error) {
      logger.error('[DbStorage] Failed to mark email as read:', { error, emailId: id });
      throw error;
    }
  }

  async markEmailAsArchived(id: number): Promise<boolean> {
    try {
      const db = await getDb();
      if (!db) {
        throw new Error('Database connection is not available');
      }
      const result = await db
        .update(emails)
        .set({ isArchived: true })
        .where(eq(emails.id, id))
        .returning({ id: emails.id });
      return result.length > 0;
    } catch (error) {
      logger.error('[DbStorage] Failed to mark email as archived:', { error, emailId: id });
      throw error;
    }
  }

  async getEmailCount(
    userId: number,
    filters?: { archived?: boolean; important?: boolean; snoozed?: boolean; trashed?: boolean }
  ): Promise<number> {
    const startTime = Date.now();
    
    // Phase 2: Check cache for count queries (these are frequently accessed)
    const cacheKey = this.getCacheKey('getEmailCount', [userId, filters]);
    const cachedResult = this.getFromCache<number>(cacheKey);
    if (cachedResult !== null) {
      this.trackQueryPerformance('getEmailCount_cached', Date.now() - startTime);
      return cachedResult;
    }

    return monitoredQuery(
      'getEmailCount',
      `SELECT COUNT(*) FROM emails WHERE user_id = ${userId} WITH FILTERS`,
      async () => {
        const db = await getDb();
        if (!db) {
          throw new Error('Database connection is not available');
        }
        
        // Phase 2: Optimized count query building
        let whereCondition: SQL<unknown> = eq(emails.userId, userId);

        // Handle filters (same logic as getEmails)
        if (filters) {
          // Trashed filter - emails moved to trash
          if (filters.trashed === true) {
            // Trash view: only count trashed emails
            whereCondition = and(whereCondition, eq(emails.isTrashed, true)) as SQL<unknown>;
          } else {
            // For all other views: exclude trashed emails by default
            whereCondition = and(whereCondition, eq(emails.isTrashed, false)) as SQL<unknown>;
          }

          // Archive filter
          if (filters.archived === true) {
            // Archive view: only count archived emails
            whereCondition = and(whereCondition, eq(emails.isArchived, true)) as SQL<unknown>;
          } else if (!filters.important && !filters.snoozed && !filters.trashed) {
            // Default inbox view: only count non-archived emails
            whereCondition = and(whereCondition, eq(emails.isArchived, false)) as SQL<unknown>;
          }

          // Important filter - emails flagged as important
          if (filters.important === true) {
            whereCondition = and(whereCondition, eq(emails.isImportant, true)) as SQL<unknown>;
          }

          // Snoozed filter - emails with a snoozedUntil date in the future
          if (filters.snoozed === true) {
            whereCondition = and(whereCondition, not(isNull(emails.snoozedUntil))) as SQL<unknown>;
          }
        } else {
          // Default behavior (inbox): count only non-archived and non-trashed emails
          whereCondition = and(
            whereCondition,
            eq(emails.isArchived, false),
            eq(emails.isTrashed, false)
          ) as SQL<unknown>;
        }

        // Execute the count query
        const result = await db.select({ count: count() }).from(emails).where(whereCondition);
        const emailCount = result[0]?.count || 0;
        
        // Phase 2: Cache count results (they change less frequently than email lists)
        this.setCache(cacheKey, emailCount, this.CACHE_TTL * 2); // Longer TTL for counts
        
        this.queryMetrics.optimizedQueries++;
        this.trackQueryPerformance('getEmailCount', Date.now() - startTime);

        return emailCount;
      },
      {
        params: [userId],
        userId,
      }
    );
  }

  // Settings operations
  async getSettings(userId: number): Promise<Settings | undefined> {
    try {
      const db = await getDb();
      if (!db) {
        throw new Error('Database connection is not available');
      }
      const result = await db.select().from(settings).where(eq(settings.userId, userId));
      return result[0];
    } catch (error) {
      logger.error('[DbStorage] Failed to get settings:', { error, userId });
      throw error;
    }
  }

  async createSettings(insertSettings: InsertSettings): Promise<Settings> {
    try {
      const db = await getDb();
      if (!db) {
        throw new Error('Database connection is not available');
      }
      const result = await db.insert(settings).values(insertSettings).returning();
      return result[0];
    } catch (error) {
      logger.error('[DbStorage] Failed to create settings:', { error, userId: insertSettings.userId });
      throw error;
    }
  }

  async updateSettings(
    userId: number,
    settingsData: Partial<Settings>
  ): Promise<Settings | undefined> {
    try {
      const db = await getDb();
      if (!db) {
        throw new Error('Database connection is not available');
      }
      const result = await db
        .update(settings)
        .set(settingsData)
        .where(eq(settings.userId, userId))
        .returning();
      return result[0];
    } catch (error) {
      logger.error('[DbStorage] Failed to update settings:', { error, userId });
      throw error;
    }
  }

  // Achievement operations
  async getAchievements(userId: number): Promise<Achievement[]> {
    try {
      const db = await getDb();
      if (!db) {
        throw new Error('Database connection is not available');
      }
      return db.select().from(achievements).where(eq(achievements.userId, userId));
    } catch (error) {
      logger.error('[DbStorage] Failed to get achievements:', { error, userId });
      throw error;
    }
  }

  async getAchievement(id: number): Promise<Achievement | undefined> {
    try {
      const db = await getDb();
      if (!db) {
        throw new Error('Database connection is not available');
      }
      const result = await db.select().from(achievements).where(eq(achievements.id, id));
      return result[0];
    } catch (error) {
      logger.error('[DbStorage] Failed to get achievement:', { error, achievementId: id });
      throw error;
    }
  }

  async getAchievementByNameAndUser(
    userId: number,
    name: string
  ): Promise<Achievement | undefined> {
    try {
      const db = await getDb();
      if (!db) {
        throw new Error('Database connection is not available');
      }
      const result = await db
        .select()
        .from(achievements)
        .where(and(eq(achievements.userId, userId), eq(achievements.name, name)));
      return result[0];
    } catch (error) {
      logger.error('[DbStorage] Failed to get achievement by name and user:', { error, userId, name });
      throw error;
    }
  }

  async createAchievement(achievement: InsertAchievement): Promise<Achievement> {
    try {
      const db = await getDb();
      if (!db) {
        throw new Error('Database connection is not available');
      }
      const result = await db.insert(achievements).values(achievement).returning();
      return result[0];
    } catch (error) {
      logger.error('[DbStorage] Failed to create achievement:', { error, achievementName: achievement.name });
      throw error;
    }
  }

  async updateAchievement(
    id: number,
    achievementData: Partial<Achievement>
  ): Promise<Achievement | undefined> {
    try {
      const db = await getDb();
      if (!db) {
        throw new Error('Database connection is not available');
      }
      const result = await db
        .update(achievements)
        .set(achievementData)
        .where(eq(achievements.id, id))
        .returning();
      return result[0];
    } catch (error) {
      logger.error('[DbStorage] Failed to update achievement:', { error, achievementId: id });
      throw error;
    }
  }

  async getTaskQueueStats(): Promise<Record<string, unknown>> {
    // Delegate to task queue service for now
    const { getTaskQueueStats } = await import('./services/taskQueue');
    return getTaskQueueStats();
  }
}
