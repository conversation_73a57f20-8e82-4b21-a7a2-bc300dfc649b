/**
 * Email Processor Service
 *
 * This service handles the processing of emails in the background.
 * It integrates with the task queue for robust processing with retry capabilities.
 */

import { type TaskQueue, taskQueue } from '@shared/schema';
import { sql } from 'drizzle-orm';
import { getDb } from '../db';
import logger from '../lib/logger';
import { storage } from '../storage';
import { updateAchievementProgress } from './achievements';
import { categorizeEmail as optimizedCategorizeEmail } from './categorization';
import { generateReplyWithGemini, summarizeEmailWithGemini } from './gemini';
import { checkRateLimit } from './rateLimiter';
import { enqueueTask, registerTaskHandler, TaskPriority, TaskStatus, TaskType } from './taskQueue';

// Constants for email status messages
const EMAIL_STATUS = {
  PROCESSING: 'Processing...',
  ERROR: 'Error generating summary',
  UNAVAILABLE: 'Summary unavailable',
  RATE_LIMITED: 'Summary unavailable (rate limited)',
  SHORT_MESSAGE: 'Short message',
};

// Constants for email categorization
const EMAIL_CATEGORIES = {
  PENDING: 'Pending',
  NOTIFICATION: 'Notification',
  UNCATEGORIZED: 'Uncategorized',
};

// Default reply template when AI cannot generate a reply
const DEFAULT_REPLY_TEMPLATE = (subject: string) =>
  `I received your email regarding "${subject}". I'll respond to you soon.`;

/**
 * Initialize the email processing handlers
 */
export function initializeEmailProcessors() {
  // Register handlers for email processing tasks
  registerTaskHandler(TaskType.EMAIL_SUMMARY, handleEmailSummaryTask);
  registerTaskHandler(TaskType.EMAIL_CATEGORIZATION, handleEmailCategorizationTask);
  registerTaskHandler(TaskType.REPLY_GENERATION, handleReplyGenerationTask);
  registerTaskHandler(TaskType.BATCH_PROCESSING, handleBatchProcessingTask);

  logger.info('Email processors initialized');
}

/**
 * Handle email summary task
 *
 * @param task The task to process
 * @returns The result of the task
 */
async function handleEmailSummaryTask(task: TaskQueue): Promise<any> {
  const taskStartTime = Date.now();
  const { emailId, userId } = task.data as { emailId: number; userId: number };

  logger.info(`Processing email summary for email ${emailId}`, {
    taskId: task.id,
    emailId,
    userId,
    taskType: TaskType.EMAIL_SUMMARY,
    attemptNumber: task.retryCount ? task.retryCount + 1 : 1,
  });

  // Get the email
  let email;
  try {
    email = await storage.getEmail(emailId);
    if (!email) {
      logger.error(`Email ${emailId} not found for summary task ${task.id}`, {
        taskId: task.id,
        emailId,
        userId,
      });
      throw new Error(`Email ${emailId} not found`);
    }

    logger.debug(`Retrieved email ${emailId} for summary task`, {
      taskId: task.id,
      emailId,
      subject: email.subject,
      contentLength: email.originalContent?.length || 0,
    });
  } catch (dbError) {
    const errorMessage = dbError instanceof Error ? dbError.message : String(dbError);
    logger.error(`Database error retrieving email ${emailId} for summary task: ${errorMessage}`, {
      taskId: task.id,
      emailId,
      userId,
      errorStack: dbError instanceof Error ? dbError.stack : 'No stack trace',
    });
    throw dbError;
  }

  // Check if the email has content to summarize
  if (!email.originalContent || email.originalContent.length < 20) {
    logger.info(
      `Email ${emailId} has no significant content to summarize (length: ${email.originalContent?.length || 0})`,
      {
        taskId: task.id,
        emailId,
        userId,
        subject: email.subject,
      }
    );

    try {
      await storage.updateEmail(emailId, {
        summary: EMAIL_STATUS.SHORT_MESSAGE,
      });
      logger.debug(`Updated email ${emailId} with SHORT_MESSAGE status`);
    } catch (updateError) {
      logger.warn(`Failed to update email ${emailId} with SHORT_MESSAGE status: ${updateError}`);
      // Continue - this is not critical
    }

    return { status: 'skipped', reason: 'short_content' };
  }

  // Check rate limit
  try {
    const isRateLimited = !(await checkRateLimit(userId.toString(), 'AI_SUMMARY', 10));
    if (isRateLimited) {
      logger.warn(`Rate limit reached for user ${userId} on AI summary for email ${emailId}`, {
        taskId: task.id,
        emailId,
        userId,
        rateLimitKey: 'AI_SUMMARY',
      });

      await storage.updateEmail(emailId, {
        summary: EMAIL_STATUS.RATE_LIMITED,
      });
      return { status: 'skipped', reason: 'rate_limited' };
    }
  } catch (rateLimitError) {
    logger.error(`Error checking rate limit for user ${userId}: ${rateLimitError}`);
    // Continue - default to allowing the operation if rate limit check fails
  }

  // Generate summary
  try {
    logger.debug(
      `Generating summary for email ${emailId} (content length: ${email.originalContent.length})`,
      {
        taskId: task.id,
        emailId,
        userId,
        contentPreview: `${email.originalContent.substring(0, 100)}...`,
      }
    );

    const summaryStartTime = Date.now();
    const summary = await summarizeEmailWithGemini(email.originalContent);
    const summaryDuration = Date.now() - summaryStartTime;

    logger.debug(
      `Generated summary for email ${emailId} in ${summaryDuration}ms (summary length: ${summary.length})`,
      {
        taskId: task.id,
        emailId,
        summaryLength: summary.length,
        processingTimeMs: summaryDuration,
      }
    );

    // Update the email
    try {
      await storage.updateEmail(emailId, { summary });

      const totalDuration = Date.now() - taskStartTime;
      logger.info(
        `Successfully generated and stored summary for email ${emailId} (total time: ${totalDuration}ms)`,
        {
          taskId: task.id,
          emailId,
          userId,
          summaryLength: summary.length,
          processingTimeMs: totalDuration,
        }
      );

      // Return the result
      return {
        status: 'success',
        summary,
        processingTimeMs: totalDuration,
        summaryLength: summary.length,
      };
    } catch (updateError) {
      logger.error(`Failed to update email ${emailId} with new summary: ${updateError}`, {
        taskId: task.id,
        emailId,
        summaryLength: summary.length,
        errorStack: updateError instanceof Error ? updateError.stack : 'No stack trace',
      });
      throw updateError;
    }
  } catch (aiError) {
    const errorMessage = aiError instanceof Error ? aiError.message : String(aiError);
    const errorStack = aiError instanceof Error ? aiError.stack : 'No stack trace';

    logger.error(`Failed to generate summary for email ${emailId}: ${errorMessage}`, {
      taskId: task.id,
      emailId,
      userId,
      errorStack,
      processingTimeMs: Date.now() - taskStartTime,
      contentLength: email.originalContent.length,
      contentPreview: `${email.originalContent.substring(0, 100)}...`,
      attemptNumber: task.retryCount ? task.retryCount + 1 : 1,
    });

    // Mark as error in database but throw to trigger task queue retry
    try {
      await storage.updateEmail(emailId, { summary: EMAIL_STATUS.ERROR });
    } catch (updateError) {
      logger.error(`Failed to update email ${emailId} error status: ${updateError}`);
      // Continue with throwing the original error
    }

    throw aiError;
  }
}

/**
 * Handle email categorization task
 *
 * @param task The task to process
 * @returns The result of the task
 */
async function handleEmailCategorizationTask(task: TaskQueue): Promise<any> {
  const { emailId, userId } = task.data as { emailId: number; userId: number };
  logger.info(`Processing email categorization for email ${emailId}`);

  // Get the email
  const email = await storage.getEmail(emailId);
  if (!email) {
    throw new Error(`Email ${emailId} not found`);
  }

  // Check if the email has content to categorize
  if (!email.originalContent || email.originalContent.length < 20) {
    logger.info(`Email ${emailId} has no significant content to categorize`);
    await storage.updateEmail(emailId, {
      categories: [EMAIL_CATEGORIES.UNCATEGORIZED],
    });
    return { status: 'skipped', reason: 'short_content' };
  }

  // Check rate limit
  if (!(await checkRateLimit(userId.toString(), 'AI_CATEGORIZATION', 10))) {
    logger.warn(`Rate limit reached for user ${userId} on AI categorization`);
    await storage.updateEmail(emailId, {
      categories: [EMAIL_CATEGORIES.UNCATEGORIZED],
    });
    return { status: 'skipped', reason: 'rate_limited' };
  }

  // Generate categories
  try {
    logger.debug(`Generating categories for email ${emailId}`);
    // Use the optimized categorization service for better performance
    // This uses a lightweight model with fast path for common email types
    const categoryResult = await optimizedCategorizeEmail(
      email.subject || '',
      email.originalContent || '',
      email.sender || ''
    );

    // Extract the categories - the optimized service returns a single category result
    // but we need an array for the database
    const categories = categoryResult?.category
      ? [categoryResult.category]
      : [EMAIL_CATEGORIES.UNCATEGORIZED];

    // Log the method used for categorization (useful for performance monitoring)
    logger.debug(
      `Email ${emailId} categorized using ${categoryResult.method || 'unknown'} method with confidence ${categoryResult.confidence}`
    );

    // Update the email
    await storage.updateEmail(emailId, { categories });

    logger.info(`Successfully categorized email ${emailId}: ${categories.join(', ')}`);

    // Return the result
    return { status: 'success', categories };
  } catch (error) {
    logger.error(`Failed to categorize email ${emailId}: ${error}`);

    // Set fallback categories but throw to trigger task queue retry
    await storage.updateEmail(emailId, {
      categories: [EMAIL_CATEGORIES.UNCATEGORIZED],
    });
    throw error;
  }
}

/**
 * Handle reply generation task
 *
 * @param task The task to process
 * @returns The result of the task
 */
async function handleReplyGenerationTask(task: TaskQueue): Promise<any> {
  const { emailId, userId } = task.data as { emailId: number; userId: number };
  logger.info(`Processing reply generation for email ${emailId}`);

  // Get the email
  const email = await storage.getEmail(emailId);
  if (!email) {
    throw new Error(`Email ${emailId} not found`);
  }

  // Get user settings for reply tone
  const settings = await storage.getSettings(userId);
  const replyTone = settings?.replyTone || 'professional';
  const customTone = settings?.customTone;

  // Check if the email has content to generate a reply for
  if (!email.originalContent || email.originalContent.length < 20) {
    logger.info(`Email ${emailId} has no significant content to generate reply for`);
    await storage.updateEmail(emailId, {
      aiReply: DEFAULT_REPLY_TEMPLATE(email.subject || ''),
    });
    return { status: 'skipped', reason: 'short_content' };
  }

  // Check rate limit
  if (!(await checkRateLimit(userId.toString(), 'AI_REPLY', 5))) {
    logger.warn(`Rate limit reached for user ${userId} on AI reply generation`);
    await storage.updateEmail(emailId, {
      aiReply: DEFAULT_REPLY_TEMPLATE(email.subject || ''),
    });
    return { status: 'skipped', reason: 'rate_limited' };
  }

  // Generate reply
  try {
    logger.debug(`Generating reply for email ${emailId}`);
    const reply = await generateReplyWithGemini(
      email.originalContent,
      email.subject || '',
      email.sender || '',
      replyTone,
      customTone || undefined
    );

    // Update the email
    await storage.updateEmail(emailId, { aiReply: reply });

    logger.info(`Successfully generated reply for email ${emailId}`);

    // Return the result
    return { status: 'success', replyLength: reply.length };
  } catch (error) {
    logger.error(`Failed to generate reply for email ${emailId}: ${error}`);

    // Set fallback reply but throw to trigger task queue retry
    await storage.updateEmail(emailId, {
      aiReply: DEFAULT_REPLY_TEMPLATE(email.subject || ''),
    });
    throw error;
  }
}

/**
 * Handle batch processing task
 * This processes multiple emails at once
 *
 * @param task The task to process
 * @returns The result of the task
 */
async function handleBatchProcessingTask(task: TaskQueue): Promise<any> {
  const batchStartTime = Date.now();
  const { emailIds, userId, processingTypes } = task.data as {
    emailIds: number[];
    userId: number;
    processingTypes: string[];
  };

  // Validate input data
  if (!Array.isArray(emailIds) || emailIds.length === 0) {
    logger.error('Invalid batch task data: emailIds is not a valid array', {
      taskId: task.id,
      userId,
      processingTypes,
      emailIds,
    });
    throw new Error('Invalid batch task data: emailIds is not a valid array');
  }

  if (!Array.isArray(processingTypes) || processingTypes.length === 0) {
    logger.error('Invalid batch task data: processingTypes is not a valid array', {
      taskId: task.id,
      userId,
      processingTypes,
      emailIdsCount: emailIds.length,
    });
    throw new Error('Invalid batch task data: processingTypes is not a valid array');
  }

  const displayedIds: (number | string)[] =
    emailIds.length <= 10 ? emailIds : [...emailIds.slice(0, 10), '...and more'];
  logger.info(`Processing batch of ${emailIds.length} emails for user ${userId}`, {
    taskId: task.id,
    userId,
    processingTypes: processingTypes.join(', '),
    emailCount: emailIds.length,
    emailIds: displayedIds,
  });

  // Process each email
  const results = {
    total: emailIds.length,
    processed: 0,
    errors: 0,
    skipped: 0,
    emailResults: {} as Record<
      number,
      {
        success: boolean;
        error?: string;
        processingTypes?: string[];
        taskIds?: number[];
      }
    >,
  };

  // Map to track created tasks for each email
  const emailTaskMap: Record<number, number[]> = {};

  // Process emails in batches to avoid overloading the database
  const PROCESSING_CHUNK_SIZE = 5;
  const emailChunks: number[][] = [];

  for (let i = 0; i < emailIds.length; i += PROCESSING_CHUNK_SIZE) {
    emailChunks.push(emailIds.slice(i, i + PROCESSING_CHUNK_SIZE));
  }

  logger.debug(
    `Split batch processing into ${emailChunks.length} chunks of up to ${PROCESSING_CHUNK_SIZE} emails each`
  );

  let chunkIndex = 0;
  for (const emailChunk of emailChunks) {
    chunkIndex++;
    logger.debug(
      `Processing chunk ${chunkIndex}/${emailChunks.length} with ${emailChunk.length} emails`
    );

    // Process each email in parallel within the chunk
    const _chunkResults = await Promise.allSettled(
      emailChunk.map(async (emailId) => {
        const emailStartTime = Date.now();
        try {
          // Verify email exists before processing
          const email = await storage.getEmail(emailId);
          if (!email) {
            logger.warn(`Email ${emailId} not found for batch processing`, {
              taskId: task.id,
              emailId,
              userId,
            });
            results.skipped++;
            results.emailResults[emailId] = {
              success: false,
              error: 'Email not found',
            };
            return;
          }

          // Update the email to show processing status
          try {
            await storage.updateEmail(emailId, {
              summary: EMAIL_STATUS.PROCESSING,
            });
          } catch (updateError) {
            logger.error(`Failed to update email ${emailId} processing status: ${updateError}`, {
              taskId: task.id,
              emailId,
              userId,
            });
            // Continue anyway - this is not a fatal error
          }

          // Create individual tasks for each processing type
          const createdTasks: number[] = [];
          const successfulTypes: string[] = [];

          for (const type of processingTypes) {
            try {
              let task;
              switch (type) {
                case 'summary':
                  task = await enqueueTask({
                    taskType: TaskType.EMAIL_SUMMARY,
                    status: TaskStatus.PENDING,
                    priority: TaskPriority.NORMAL,
                    data: { emailId, userId },
                  });
                  break;
                case 'categorization':
                  task = await enqueueTask({
                    taskType: TaskType.EMAIL_CATEGORIZATION,
                    status: TaskStatus.PENDING,
                    priority: TaskPriority.NORMAL,
                    data: { emailId, userId },
                  });
                  break;
                case 'reply':
                  task = await enqueueTask({
                    taskType: TaskType.REPLY_GENERATION,
                    status: TaskStatus.PENDING,
                    priority: TaskPriority.NORMAL,
                    data: { emailId, userId },
                  });
                  break;
                default:
                  logger.warn(`Unknown processing type "${type}" for email ${emailId}, skipping`);
                  continue;
              }

              if (task) {
                createdTasks.push(task.id);
                successfulTypes.push(type);
                logger.debug(`Created ${type} task ${task.id} for email ${emailId}`);
              }
            } catch (taskError) {
              const errorMessage =
                taskError instanceof Error ? taskError.message : String(taskError);
              logger.error(`Failed to create ${type} task for email ${emailId}: ${errorMessage}`, {
                taskId: task.id,
                emailId,
                userId,
                processingType: type,
                errorStack: taskError instanceof Error ? taskError.stack : 'No stack trace',
              });
              // Continue with other types
            }
          }

          // If no tasks were created, treat as an error
          if (createdTasks.length === 0) {
            throw new Error('Failed to create any processing tasks');
          }

          // Track results
          results.processed++;
          results.emailResults[emailId] = {
            success: true,
            processingTypes: successfulTypes,
            taskIds: createdTasks,
          };
          emailTaskMap[emailId] = createdTasks;

          const emailDuration = Date.now() - emailStartTime;
          logger.debug(
            `Completed processing setup for email ${emailId} in ${emailDuration}ms (created ${createdTasks.length} tasks)`
          );
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : String(error);
          const errorStack = error instanceof Error ? error.stack : 'No stack trace';

          logger.error(`Failed to process email ${emailId} in batch: ${errorMessage}`, {
            taskId: task.id,
            emailId,
            userId,
            errorStack,
            processingTimeMs: Date.now() - emailStartTime,
          });

          results.errors++;
          results.emailResults[emailId] = {
            success: false,
            error: errorMessage,
          };
        }
      })
    );

    logger.debug(`Completed chunk ${chunkIndex}/${emailChunks.length}`);

    // Log progress after each chunk
    const processedSoFar = Object.keys(results.emailResults).length;
    const progressPercent = Math.round((processedSoFar / emailIds.length) * 100);
    logger.info(
      `Batch processing progress: ${processedSoFar}/${emailIds.length} emails (${progressPercent}%)`
    );
  }

  const totalDuration = Date.now() - batchStartTime;
  const avgTimePerEmail = emailIds.length > 0 ? Math.round(totalDuration / emailIds.length) : 0;

  logger.info(
    `Batch processing completed: ${results.processed}/${results.total} emails processed with ${results.errors} errors and ${results.skipped} skipped in ${totalDuration}ms (avg ${avgTimePerEmail}ms per email)`,
    {
      taskId: task.id,
      userId,
      processingTypes: processingTypes.join(', '),
      totalEmails: results.total,
      processedEmails: results.processed,
      errorCount: results.errors,
      skippedCount: results.skipped,
      totalDurationMs: totalDuration,
      avgTimePerEmailMs: avgTimePerEmail,
      totalTasksCreated: Object.values(emailTaskMap).reduce((sum, tasks) => sum + tasks.length, 0),
    }
  );

  // Update user stats
  try {
    await updateAchievementProgress(userId, 'email_processing', results.processed, Date.now());
    logger.debug(`Updated achievement progress for user ${userId}`);
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error(`Failed to update achievements for user ${userId}: ${errorMessage}`, {
      taskId: task.id,
      userId,
      processedCount: results.processed,
      errorStack: error instanceof Error ? error.stack : 'No stack trace',
    });
  }

  return results;
}

/**
 * Enqueue an email for processing
 *
 * @param emailId The ID of the email to process
 * @param userId The ID of the user who owns the email
 * @param priority The priority of the task
 * @param processingTypes The types of processing to perform
 * @returns The enqueued tasks
 */
export async function enqueueEmailProcessing(
  emailId: number,
  userId: number,
  priority: TaskPriority = TaskPriority.NORMAL,
  processingTypes: string[] = ['summary', 'categorization', 'reply']
): Promise<TaskQueue[]> {
  logger.info(`Enqueueing email ${emailId} for processing (${processingTypes.join(', ')})`);

  // Update the email to show processing status
  if (processingTypes.includes('summary')) {
    await storage.updateEmail(emailId, { summary: EMAIL_STATUS.PROCESSING });
  }

  // Create tasks for each processing type
  const tasks: TaskQueue[] = [];

  for (const type of processingTypes) {
    let task;

    switch (type) {
      case 'summary':
        task = await enqueueTask({
          taskType: TaskType.EMAIL_SUMMARY,
          status: TaskStatus.PENDING,
          priority,
          data: { emailId, userId },
        });
        break;
      case 'categorization':
        task = await enqueueTask({
          taskType: TaskType.EMAIL_CATEGORIZATION,
          status: TaskStatus.PENDING,
          priority,
          data: { emailId, userId },
        });
        break;
      case 'reply':
        task = await enqueueTask({
          taskType: TaskType.REPLY_GENERATION,
          status: TaskStatus.PENDING,
          priority,
          data: { emailId, userId },
        });
        break;
    }

    if (task) {
      tasks.push(task);
    }
  }

  return tasks;
}

/**
 * Enqueue a batch of emails for processing
 *
 * @param emailIds The IDs of the emails to process
 * @param userId The ID of the user who owns the emails
 * @param priority The priority of the task
 * @param processingTypes The types of processing to perform
 * @returns The enqueued batch task
 */
export async function enqueueBatchEmailProcessing(
  emailIds: number[],
  userId: number,
  priority: TaskPriority = TaskPriority.NORMAL,
  processingTypes: string[] = ['summary', 'categorization', 'reply']
): Promise<TaskQueue> {
  logger.info(`Enqueueing batch of ${emailIds.length} emails for processing`);

  // For large batches, split into smaller batches
  const MAX_BATCH_SIZE = 25;
  if (emailIds.length > MAX_BATCH_SIZE) {
    logger.info(`Splitting batch of ${emailIds.length} emails into smaller batches`);

    const batches: number[][] = [];
    for (let i = 0; i < emailIds.length; i += MAX_BATCH_SIZE) {
      batches.push(emailIds.slice(i, i + MAX_BATCH_SIZE));
    }

    const batchTasks: TaskQueue[] = [];
    for (const batch of batches) {
      const task = await enqueueBatchEmailProcessing(batch, userId, priority, processingTypes);
      batchTasks.push(task);
    }

    // Return the first batch task as representative
    return batchTasks[0];
  }

  // Create a batch task
  const task = await enqueueTask({
    taskType: TaskType.BATCH_PROCESSING,
    status: TaskStatus.PENDING,
    priority,
    data: { emailIds, userId, processingTypes },
  });

  return task;
}

/**
 * Re-process an email with errors
 *
 * @param emailId The ID of the email to re-process
 * @param userId The ID of the user who owns the email
 * @param processingTypes The types of processing to perform
 * @returns The enqueued tasks
 */
export async function reprocessEmailWithErrors(
  emailId: number,
  userId: number,
  processingTypes: string[] = ['summary', 'categorization', 'reply']
): Promise<TaskQueue[]> {
  logger.info(`Re-processing email ${emailId} with previous errors`);

  // Get the email
  const email = await storage.getEmail(emailId);
  if (!email) {
    throw new Error(`Email ${emailId} not found`);
  }

  // Determine if each processing type had errors
  const typesToProcess = processingTypes.filter((type) => {
    if (
      type === 'summary' &&
      (!email.summary ||
        email.summary === EMAIL_STATUS.ERROR ||
        email.summary === EMAIL_STATUS.UNAVAILABLE)
    ) {
      return true;
    }

    if (
      type === 'categorization' &&
      (!email.categories ||
        email.categories.length === 0 ||
        email.categories.includes(EMAIL_CATEGORIES.UNCATEGORIZED))
    ) {
      return true;
    }

    if (
      type === 'reply' &&
      (!email.aiReply || email.aiReply === DEFAULT_REPLY_TEMPLATE(email.subject || ''))
    ) {
      return true;
    }

    return false;
  });

  // If no errors were found, don't reprocess
  if (typesToProcess.length === 0) {
    logger.info(`No errors found for email ${emailId}, skipping reprocessing`);
    return [];
  }

  // Enqueue at high priority
  return await enqueueEmailProcessing(emailId, userId, TaskPriority.HIGH, typesToProcess);
}

/**
 * Get the status of email processing tasks
 *
 * @param emailId The ID of the email
 * @returns The status of the processing tasks
 */
export async function getEmailProcessingStatus(emailId: number): Promise<{
  hasPendingTasks: boolean;
  pendingTasks: number;
  failedTasks: number;
  completedTasks: number;
  processingTypes: string[];
  processingTasks?: any[];
  failedTaskDetails?: any[];
}> {
  const startTime = Date.now();
  logger.debug(`Getting processing status for email ${emailId}`);

  try {
    const db = await getDb();
    // Get tasks for this email
    const tasks = await db
      .select()
      .from(taskQueue)
      .where(sql`${taskQueue.data}->>'emailId' = ${emailId.toString()}`);

    if (tasks.length === 0) {
      logger.info(`No processing tasks found for email ${emailId}`);
      return {
        hasPendingTasks: false,
        pendingTasks: 0,
        failedTasks: 0,
        completedTasks: 0,
        processingTypes: [],
      };
    }

    logger.debug(`Found ${tasks.length} tasks for email ${emailId}`);

    // Filter tasks by status
    const pendingTasks = tasks.filter((t: TaskQueue) => t.status === TaskStatus.PENDING);
    const processingTasks = tasks.filter((t: TaskQueue) => t.status === TaskStatus.PROCESSING);
    const retryTasks = tasks.filter((t: TaskQueue) => t.status === TaskStatus.RETRY);
    const failedTasks = tasks.filter((t: TaskQueue) => t.status === TaskStatus.FAILED);
    const completedTasks = tasks.filter((t: TaskQueue) => t.status === TaskStatus.COMPLETED);

    // Get processing types
    const processingTypes = [...new Set(tasks.map((t: TaskQueue) => t.taskType))] as string[];

    // Extract details about processing tasks
    const allPendingTasks = [...pendingTasks, ...processingTasks, ...retryTasks];
    const pendingTasksDetails = allPendingTasks.map((t: TaskQueue) => ({
      id: t.id,
      type: t.taskType,
      status: t.status,
      priority: t.priority,
      retryCount: t.retryCount || 0,
      createdAt: t.createdAt,
      lockedAt: t.lockedAt,
      lockedBy: t.lockedBy,
      lastAttemptAt: t.lastAttemptAt,
      scheduledFor: t.scheduledFor,
    }));

    // Extract details about failed tasks
    const failedTasksDetails = failedTasks.map((t: TaskQueue) => ({
      id: t.id,
      type: t.taskType,
      retryCount: t.retryCount || 0,
      maxRetries: t.maxRetries || 3,
      lastAttemptAt: t.lastAttemptAt,
      error: t.error,
    }));

    // Calculate processing time
    const processingTimeMs = Date.now() - startTime;

    // Check if we have any processing tasks that might be stuck
    const now = new Date();
    const potentiallyStuckTasks = processingTasks.filter((t: TaskQueue) => {
      if (!t.lockedAt) return false;
      const lockDate = new Date(t.lockedAt);
      const minutesLocked = (now.getTime() - lockDate.getTime()) / (60 * 1000);
      return minutesLocked > 5; // Consider tasks locked for more than 5 minutes as potentially stuck
    });

    if (potentiallyStuckTasks.length > 0) {
      logger.warn(
        `Found ${potentiallyStuckTasks.length} potentially stuck tasks for email ${emailId}`,
        {
          emailId,
          stuckTaskIds: potentiallyStuckTasks.map((t) => t.id),
          stuckTaskTypes: potentiallyStuckTasks.map((t) => t.taskType),
          oldestLockTime: Math.min(
            ...potentiallyStuckTasks.map((t) =>
              t.lockedAt ? new Date(t.lockedAt).getTime() : Date.now()
            )
          ),
        }
      );
    }

    logger.info(
      `Email ${emailId} processing status: ${allPendingTasks.length} pending, ${failedTasks.length} failed, ${completedTasks.length} completed (processed in ${processingTimeMs}ms)`,
      {
        emailId,
        pendingCount: pendingTasks.length,
        processingCount: processingTasks.length,
        retryCount: retryTasks.length,
        failedCount: failedTasks.length,
        completedCount: completedTasks.length,
        processingTypes,
        processingTimeMs,
      }
    );

    return {
      hasPendingTasks: allPendingTasks.length > 0,
      pendingTasks: allPendingTasks.length,
      failedTasks: failedTasks.length,
      completedTasks: completedTasks.length,
      processingTypes,
      processingTasks: pendingTasksDetails,
      failedTaskDetails: failedTasksDetails,
    };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    const errorStack = error instanceof Error ? error.stack : 'No stack trace';

    logger.error(`Error getting email processing status for email ${emailId}: ${errorMessage}`, {
      emailId,
      errorStack,
      processingTimeMs: Date.now() - startTime,
    });

    // Return a minimal response with error info instead of throwing
    return {
      hasPendingTasks: false,
      pendingTasks: 0,
      failedTasks: 0,
      completedTasks: 0,
      processingTypes: [],
      error: errorMessage,
    } as any;
  }
}

/**
 * Automatically detect and fix emails with error summaries for all users
 * This function should be called periodically by a scheduled task
 *
 * @returns Object with information about fixed emails
 */
export async function autoFixEmailsWithErrorSummaries(): Promise<{
  totalUsersProcessed: number;
  totalEmailsChecked: number;
  totalErrorsFound: number;
  totalEmailsFixed: number;
  userResults: Record<
    number,
    {
      emailsChecked: number;
      errorsFound: number;
      emailsFixed: number;
    }
  >;
}> {
  logger.info('Starting automatic detection and fixing of emails with error summaries');

  // Get all users
  const users = await storage.getAllUsers();

  // Initialize result object
  const result = {
    totalUsersProcessed: 0,
    totalEmailsChecked: 0,
    totalErrorsFound: 0,
    totalEmailsFixed: 0,
    userResults: {} as Record<
      number,
      {
        emailsChecked: number;
        errorsFound: number;
        emailsFixed: number;
      }
    >,
  };

  // Process each user's emails
  for (const user of users) {
    try {
      logger.info(`Checking emails with error summaries for user ${user.id} (${user.email})`);

      // Get all emails for the user (limit to 100 most recent)
      const emails = await storage.getEmails(user.id, 100);

      // Initialize user result
      result.userResults[user.id] = {
        emailsChecked: emails.length,
        errorsFound: 0,
        emailsFixed: 0,
      };

      // Update total emails checked
      result.totalEmailsChecked += emails.length;

      // Find emails with error summaries using comprehensive pattern matching
      const errorEmails = emails.filter(
        (email) =>
          email.summary === 'Error generating summary' ||
          email.summary === 'Summary unavailable' ||
          email.summary === 'Processing...' ||
          !email.summary ||
          email.summary === '' ||
          (email.summary &&
            (email.summary.toLowerCase().includes('error') ||
              email.summary.toLowerCase().includes('failed') ||
              email.summary.toLowerCase().includes('unavailable') ||
              email.summary.toLowerCase().includes('api limit') ||
              email.summary.toLowerCase().includes('could not')))
      );

      // Update error count
      result.userResults[user.id].errorsFound = errorEmails.length;
      result.totalErrorsFound += errorEmails.length;

      logger.info(`Found ${errorEmails.length} emails with error summaries for user ${user.id}`);

      // Process up to 10 emails per user to avoid rate limiting
      for (const email of errorEmails.slice(0, 10)) {
        try {
          // Only process emails that have content
          if (!email.originalContent || email.originalContent.length < 20) {
            logger.info(`Skipping email ${email.id} - insufficient content`);
            continue;
          }

          logger.info(`Auto-fixing email ${email.id} with error summary`);

          // Reprocess the email
          const tasks = await reprocessEmailWithErrors(email.id, user.id);

          if (tasks.length > 0) {
            result.userResults[user.id].emailsFixed++;
            result.totalEmailsFixed++;
            logger.info(
              `Successfully queued email ${email.id} for reprocessing with ${tasks.length} tasks`
            );
          }
        } catch (emailError) {
          logger.error(`Error reprocessing email ${email.id} for user ${user.id}: ${emailError}`);
        }
      }

      // Update total users processed
      result.totalUsersProcessed++;
    } catch (userError) {
      logger.error(`Error processing user ${user.id}: ${userError}`);
    }
  }

  logger.info(
    `Auto-fix complete. Processed ${result.totalUsersProcessed} users, found ${result.totalErrorsFound} emails with errors, fixed ${result.totalEmailsFixed} emails`
  );

  return result;
}
