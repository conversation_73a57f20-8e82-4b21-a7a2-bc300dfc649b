-- WARNING: This schema is for context only and is not meant to be run.
-- Table order and constraints may not be valid for execution.

CREATE TABLE public.InboxZero (
  id bigint GENERATED ALWAYS AS IDENTITY NOT NULL,
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  CONSTRAINT InboxZero_pkey PRIMARY KEY (id)
);
CREATE TABLE public.achievements (
  id bigint GENERATED ALWAYS AS IDENTITY NOT NULL,
  user_id bigint NOT NULL,
  name text NOT NULL,
  description text NOT NULL,
  type text NOT NULL,
  level integer NOT NULL DEFAULT 1,
  icon text NOT NULL,
  unlocked_at timestamp without time zone NOT NULL DEFAULT now(),
  progress integer NOT NULL DEFAULT 0,
  max_progress integer NOT NULL,
  is_complete boolean NOT NULL DEFAULT false,
  CONSTRAINT achievements_pkey PRIMARY KEY (id)
);
CREATE TABLE public.emails (
  id bigint GENERATED ALWAYS AS IDENTITY NOT NULL,
  user_id bigint NOT NULL,
  message_id text NOT NULL,
  thread_id text,
  subject text,
  snippet text,
  sender text,
  sender_email text,
  received_at timestamp without time zone,
  is_read boolean DEFAULT false,
  is_archived boolean DEFAULT false,
  is_replied boolean DEFAULT false,
  is_trashed boolean DEFAULT false,
  is_important boolean DEFAULT false,
  snoozed_until timestamp without time zone,
  reply_date timestamp without time zone,
  reply_id text,
  summary text,
  categories ARRAY,
  priority text,
  ai_reply text,
  original_content text,
  html_content text,
  provider text,
  label_ids ARRAY,
  content_expires_at timestamp without time zone,
  last_accessed timestamp without time zone DEFAULT now(),
  is_content_encrypted boolean DEFAULT false,
  retention_days integer DEFAULT 30,
  CONSTRAINT emails_pkey PRIMARY KEY (id)
);
CREATE TABLE public.security_logs (
  id integer NOT NULL DEFAULT nextval('security_logs_id_seq'::regclass),
  user_id integer NOT NULL,
  event_type text NOT NULL,
  description text NOT NULL,
  ip_address text,
  user_agent text,
  created_at timestamp without time zone NOT NULL DEFAULT now(),
  CONSTRAINT security_logs_pkey PRIMARY KEY (id)
);
CREATE TABLE public.session (
  sid character varying NOT NULL,
  sess json NOT NULL,
  expire timestamp without time zone NOT NULL,
  CONSTRAINT session_pkey PRIMARY KEY (sid)
);
CREATE TABLE public.settings (
  id bigint GENERATED ALWAYS AS IDENTITY NOT NULL,
  user_id bigint NOT NULL UNIQUE,
  reply_tone text DEFAULT 'professional'::text,
  display_name text,
  custom_tone text,
  privacy_mode boolean DEFAULT false,
  notification_digest boolean DEFAULT true,
  categories jsonb,
  priority_colors jsonb,
  theme_mode text DEFAULT 'system'::text,
  accent_color text DEFAULT 'indigo'::text,
  border_radius integer DEFAULT 6,
  created_at timestamp without time zone DEFAULT now(),
  updated_at timestamp without time zone DEFAULT now(),
  data_retention_days integer DEFAULT 30,
  allow_ai_processing boolean DEFAULT true,
  store_email_content boolean DEFAULT true,
  auto_delete_processed_emails boolean DEFAULT false,
  encrypt_sensitive_data boolean DEFAULT true,
  consent_version text DEFAULT '1.0'::text,
  consent_timestamp timestamp without time zone DEFAULT now(),
  CONSTRAINT settings_pkey PRIMARY KEY (id)
);
CREATE TABLE public.task_queue (
  id bigint GENERATED ALWAYS AS IDENTITY NOT NULL,
  task_type text NOT NULL,
  status text NOT NULL DEFAULT 'pending'::text,
  priority integer NOT NULL DEFAULT 1,
  data jsonb NOT NULL,
  result jsonb,
  error text,
  retry_count integer NOT NULL DEFAULT 0,
  max_retries integer NOT NULL DEFAULT 3,
  last_attempt_at timestamp without time zone,
  created_at timestamp without time zone NOT NULL DEFAULT now(),
  scheduled_for timestamp without time zone,
  completed_at timestamp without time zone,
  locked_by text,
  locked_at timestamp without time zone,
  CONSTRAINT task_queue_pkey PRIMARY KEY (id)
);
CREATE TABLE public.users (
  id bigint GENERATED ALWAYS AS IDENTITY NOT NULL,
  email text NOT NULL UNIQUE,
  name text,
  picture text,
  access_token text,
  refresh_token text,
  expires_at timestamp without time zone,
  provider text NOT NULL DEFAULT 'google'::text,
  firebase_uid text,
  gmail_tokens jsonb,
  emails_processed integer DEFAULT 0,
  replies_sent integer DEFAULT 0,
  tier text DEFAULT 'free'::text,
  reply_tone text DEFAULT 'professional'::text,
  last_login timestamp without time zone,
  last_reply_date timestamp without time zone,
  refresh_attempts integer DEFAULT 0,
  last_token_refresh timestamp without time zone,
  auth_error_count integer DEFAULT 0,
  security_level text DEFAULT 'standard'::text,
  token_invalid boolean DEFAULT false,
  last_token_error text,
  last_api_error text,
  last_connection_verified timestamp without time zone,
  token_update_status text,
  token_status text DEFAULT 'unknown'::text,
  token_error_count integer DEFAULT 0,
  token_error_time timestamp without time zone,
  token_last_refreshed timestamp without time zone,
  role character varying DEFAULT 'user'::character varying,
  CONSTRAINT users_pkey PRIMARY KEY (id)
);