import {
  boolean,
  index,
  integer,
  jsonb,
  pgTable,
  serial,
  text,
  timestamp,
} from 'drizzle-orm/pg-core';
import { createInsertSchema } from 'drizzle-zod';
import type { z } from 'zod';

// Task queue for background processing
export const taskQueue = pgTable(
  'task_queue',
  {
    id: serial('id').primaryKey(),
    taskType: text('task_type').notNull(), // 'email_summary', 'email_categorization', 'reply_generation', etc.
    status: text('status').notNull().default('pending'), // 'pending', 'processing', 'completed', 'failed', 'retry'
    priority: integer('priority').notNull().default(1), // Higher value = higher priority
    data: jsonb('data').notNull(), // Task specific data (emailId, userId, etc.)
    result: jsonb('result'), // Result of the task execution
    error: text('error'), // Error message if task failed
    retryCount: integer('retry_count').notNull().default(0), // Number of retry attempts
    maxRetries: integer('max_retries').notNull().default(3), // Maximum number of retry attempts
    lastAttemptAt: timestamp('last_attempt_at'), // When the task was last attempted
    createdAt: timestamp('created_at').notNull().defaultNow(), // When the task was created
    scheduledFor: timestamp('scheduled_for'), // When the task should be executed (for delayed tasks)
    completedAt: timestamp('completed_at'), // When the task was completed
    lockedBy: text('locked_by'), // Worker ID that is currently processing this task
    lockedAt: timestamp('locked_at'), // When the task was locked for processing
  },
  (table) => {
    return {
      statusIndex: index('task_queue_status_idx').on(table.status),
      priorityIndex: index('task_queue_priority_idx').on(table.priority),
      taskTypeIndex: index('task_queue_type_idx').on(table.taskType),
      scheduledIndex: index('task_queue_scheduled_idx').on(table.scheduledFor),
    };
  }
);

export const users = pgTable('users', {
  id: serial('id').primaryKey(),
  email: text('email').notNull().unique(),
  name: text('name'),
  picture: text('picture'),
  accessToken: text('access_token'),
  refreshToken: text('refresh_token'),
  expiresAt: timestamp('expires_at'),
  provider: text('provider').notNull(), // "google", "outlook", or "firebase"
  firebaseUid: text('firebase_uid'), // Firebase User ID for authentication
  gmailTokens: jsonb('gmail_tokens'), // Store Gmail-specific tokens as JSON
  emailsProcessed: integer('emails_processed').default(0),
  repliesSent: integer('replies_sent').default(0),
  tier: text('tier').default('free'),
  replyTone: text('reply_tone').default('professional'),
  lastLogin: timestamp('last_login'),
  lastReplyDate: timestamp('last_reply_date'),
  refreshAttempts: integer('refresh_attempts').default(0), // Track failed refresh attempts
  lastTokenRefresh: timestamp('last_token_refresh'), // When token was last refreshed
  authErrorCount: integer('auth_error_count').default(0), // Track authentication errors
  securityLevel: text('security_level').default('standard'), // Security preference level
  // Added fields for token status tracking and error handling
  tokenInvalid: boolean('token_invalid').default(false), // Flag to indicate if token is invalid
  lastTokenError: text('last_token_error'), // Store last token error message
  lastApiError: text('last_api_error'), // Store last API error message
  lastConnectionVerified: timestamp('last_connection_verified'), // When connection was last verified
  tokenUpdateStatus: text('token_update_status'), // Status of last token update attempt
  // Additional token tracking fields for admin functionality
  tokenStatus: text('token_status').default('unknown'), // Status of token: valid, invalid, expired, revoked
  tokenErrorCount: integer('token_error_count').default(0), // Count of token errors
  tokenErrorTime: timestamp('token_error_time'), // When the last token error occurred
  tokenLastRefreshed: timestamp('token_last_refreshed'), // When token was last successfully refreshed
  role: text('role').default('user'), // User role: 'admin' or 'user'
});

export const emails = pgTable('emails', {
  id: serial('id').primaryKey(),
  userId: integer('user_id').notNull(),
  messageId: text('message_id').notNull(),
  threadId: text('thread_id'),
  subject: text('subject'),
  snippet: text('snippet'),
  sender: text('sender'),
  senderEmail: text('sender_email'),
  receivedAt: timestamp('received_at'),
  isRead: boolean('is_read').default(false),
  isArchived: boolean('is_archived').default(false),
  isReplied: boolean('is_replied').default(false),
  isTrashed: boolean('is_trashed').default(false),
  isImportant: boolean('is_important').default(false),
  snoozedUntil: timestamp('snoozed_until'),
  replyDate: timestamp('reply_date'),
  replyId: text('reply_id'),
  summary: text('summary'),
  categories: text('categories').array().default([]),
  priority: text('priority'),
  aiReply: text('ai_reply'),
  originalContent: text('original_content'),
  htmlContent: text('html_content'), // Store HTML version of email
  provider: text('provider'), // Email provider: "gmail", "outlook", etc.
  labelIds: text('label_ids').array().default([]), // Gmail label IDs
  // Privacy and data retention fields
  contentExpiresAt: timestamp('content_expires_at'), // When email content should be automatically deleted
  lastAccessed: timestamp('last_accessed').defaultNow(), // Track when email was last accessed
  isContentEncrypted: boolean('is_content_encrypted').default(false), // Track if sensitive content is encrypted
  retentionDays: integer('retention_days').default(30), // User-configurable retention period
});

export const settings = pgTable('settings', {
  id: serial('id').primaryKey(),
  userId: integer('user_id')
    .notNull()
    .references(() => users.id, { onDelete: 'cascade' }),
  replyTone: text('reply_tone').default('professional'),
  displayName: text('display_name'),
  customTone: text('custom_tone'),
  privacyMode: boolean('privacy_mode').default(false),
  notificationDigest: boolean('notification_digest').default(true),
  categories: jsonb('categories'),
  priorityColors: jsonb('priority_colors'),
  themeMode: text('theme_mode').default('system'),
  borderRadius: integer('border_radius').default(6),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
  // Enhanced privacy settings
  dataRetentionDays: integer('data_retention_days').default(30), // User-configurable data retention
  allowAIProcessing: boolean('allow_ai_processing').default(true), // Consent for AI processing
  storeEmailContent: boolean('store_email_content').default(true), // Option to disable content storage
  autoDeleteProcessedEmails: boolean('auto_delete_processed_emails').default(false), // Auto-delete after processing
  encryptSensitiveData: boolean('encrypt_sensitive_data').default(true), // Encrypt email content
  consentVersion: text('consent_version').default('1.0'), // Track consent version for compliance
  consentTimestamp: timestamp('consent_timestamp').defaultNow(), // When consent was given
});

export const insertUserSchema = createInsertSchema(users).omit({
  id: true,
  emailsProcessed: true,
  repliesSent: true,
});

export const insertEmailSchema = createInsertSchema(emails).omit({
  id: true,
});

export const insertSettingsSchema = createInsertSchema(settings).omit({
  id: true,
});

// Achievements table
export const achievements = pgTable('achievements', {
  id: serial('id').primaryKey(),
  userId: integer('user_id')
    .notNull()
    .references(() => users.id),
  name: text('name').notNull(),
  description: text('description').notNull(),
  type: text('type').notNull(), // 'inbox', 'reply', 'category', 'streak', etc.
  level: integer('level').notNull().default(1),
  icon: text('icon').notNull(),
  unlockedAt: timestamp('unlocked_at').notNull().defaultNow(),
  progress: integer('progress').notNull().default(0),
  maxProgress: integer('max_progress').notNull(),
  isComplete: boolean('is_complete').notNull().default(false),
});

export const insertAchievementSchema = createInsertSchema(achievements).omit({
  id: true,
});

// Insert schema for task queue
export const insertTaskQueueSchema = createInsertSchema(taskQueue).omit({
  id: true,
  createdAt: true,
  completedAt: true,
  lockedAt: true,
});

export type InsertUser = z.infer<typeof insertUserSchema>;
export type InsertEmail = z.infer<typeof insertEmailSchema>;
export type InsertSettings = z.infer<typeof insertSettingsSchema>;
export type InsertAchievement = z.infer<typeof insertAchievementSchema>;
export type InsertTaskQueue = z.infer<typeof insertTaskQueueSchema>;

export type BaseUser = typeof users.$inferSelect;

/**
 * LEGACY CLEANUP PHASE 3: Direct Schema Field Usage
 *
 * Removed legacy alias interface to force migration to actual database field names.
 * This eliminates Drizzle ORM type confusion and forces explicit field usage.
 *
 * MIGRATION GUIDE:
 * - Use user.gmailTokens instead of user.gmail_tokens
 * - Use user.tokenInvalid instead of user.token_invalid
 * - Use user.firebaseUid instead of user.firebase_uid
 * - Use user.tokenInvalidReason instead of user.token_invalid_reason
 *
 * This change forces compile-time errors for any remaining legacy usage,
 * ensuring complete migration to standard database field names.
 */
export type User = BaseUser;

export type Email = typeof emails.$inferSelect;

export type Settings = typeof settings.$inferSelect;
export type Achievement = typeof achievements.$inferSelect;
export type TaskQueue = typeof taskQueue.$inferSelect;

// Stats type for frontend usage
export type Stats = {
  totalEmails: number;
  readEmails: number;
  archivedEmails: number;
  repliedEmails: number;
  emailsProcessed: number;
  categoryCounts: Record<string, number>;
  priorityCounts: Record<string, number>;
  lastDayActivity: number;
  lastWeekActivity: number;
  lastMonthActivity: number;
  streakDays: number;
  averageResponseTime: number;
  inboxZeroCount?: number;
  responseRate?: number;
};
