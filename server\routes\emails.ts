/**
 * Email Routes
 *
 * Core email API endpoints for managing emails. All business logic is
 * delegated to the emailService.
 */

import { type Request, type Response, Router } from 'express';
import logger from '../lib/logger';
import { ErrorCode, sendError, sendSuccess } from '../lib/standardizedResponses';
import { emailService } from '../services/email-v2';
import tokenService from '../services/tokenService';
import { fetchGmailEmails, processEmailsWithAI } from '../services/email-v2';
import { storage } from '../storage';
import { dataRetentionService } from '../services/dataRetention';
import { catchAsync } from '../utils/errorHandler';
import { EmailSchemas, validateRequest } from '../utils/inputValidator';
import { z } from 'zod';

const router = Router();

/**
 * GET /api/emails
 * Get emails with pagination and filtering
 */
router.get(
  '/',
  catchAsync(async (req: Request, res: Response) => {
    const userId = req.user!.id;
    const {
      limit: limitStr = '50',
      offset: offsetStr = '0',
      archived,
      important,
      snoozed,
      trashed,
    } = req.query;
    const limit = Number.parseInt(limitStr as string, 10);
    const offset = Number.parseInt(offsetStr as string, 10);

    const filters = {
      archived: archived === 'true' || undefined,
      important: important === 'true' || undefined,
      snoozed: snoozed === 'true' || undefined,
      trashed: trashed === 'true' || undefined,
    };

    // The service layer likely expects page-based pagination, let's calculate it.
    const page = Math.floor(offset / limit) + 1;
    const result = await emailService.getEmails(userId, { page, limit, filters });
    
    // Debug logging to help troubleshoot email ordering issues
    if (result.emails && result.emails.length > 0) {
      const emailDates = result.emails.slice(0, 5).map(email => ({
        id: email.id,
        subject: email.subject?.substring(0, 50),
        receivedAt: email.receivedAt,
        messageId: email.messageId
      }));
      logger.info('Email query result (first 5 emails)', {
        userId,
        totalEmails: result.totalEmails,
        emailDates,
        filters,
        page,
        limit
      });
    }
    
    return res.status(200).json(result);
  })
);

/**
 * GET /api/emails/processing-status
 * Get the current status of email processing (how many emails need AI processing, have errors, etc.)
 */
router.get(
  '/processing-status',
  catchAsync(async (req: Request, res: Response) => {
    const userId = req.user!.id;

    try {
      // Get all emails for the user (increased limit to get better overview)
      const allEmails = await storage.getEmails(userId, 100, 0);
      
      // Get the actual last sync time from the automatic sync service
      let lastSyncTime = new Date().toISOString();
      try {
        const { getAutoSyncStats } = await import('../services/automaticEmailSync');
        const autoSyncStats = getAutoSyncStats();
        if (autoSyncStats.lastSyncStats.lastSyncTime) {
          lastSyncTime = autoSyncStats.lastSyncStats.lastSyncTime.toISOString();
        }
      } catch (autoSyncError) {
        logger.debug('Failed to get auto sync stats', { autoSyncError });
      }
      
      // Count emails by processing status
      const stats = {
        total: allEmails.length,
        processing: 0,
        errors: 0,
        completed: 0,
        needsProcessing: 0,
        lastSyncTime,
      };

      for (const email of allEmails) {
        if (!email.summary || email.summary.trim() === '') {
          stats.needsProcessing++;
        } else if (email.summary === 'Processing...') {
          stats.processing++;
        } else if (
          email.summary === 'Error generating summary' ||
          email.summary === 'Summary unavailable' ||
          email.summary.toLowerCase().includes('error') ||
          email.summary.toLowerCase().includes('failed') ||
          email.summary.toLowerCase().includes('unavailable')
        ) {
          stats.errors++;
        } else {
          stats.completed++;
        }
      }

      // Get task queue status for this user's emails
      try {
        const { getTaskQueueStats } = await import('../services/taskQueue');
        const taskStats = await getTaskQueueStats();
        
        return sendSuccess(res, {
          ...stats,
          taskQueue: {
            isRunning: taskStats.processor.isRunning,
            pendingTasks: taskStats.status.pending || 0,
            processingTasks: taskStats.status.processing || 0,
          }
        }, 'Processing status retrieved successfully');
      } catch (taskError) {
        logger.warn('Failed to get task queue stats', { taskError });
        return sendSuccess(res, stats, 'Processing status retrieved successfully (without task queue info)');
      }
    } catch (error) {
      logger.error('Failed to get processing status', error, { userId });
      return sendError(res, ErrorCode.INTERNAL_ERROR, 'Failed to get processing status');
    }
  })
);

/**
 * GET /api/emails/:id
 * Get single email by ID
 */
router.get(
  '/:id',
  catchAsync(async (req: Request, res: Response) => {
    const userId = req.user!.id;
    const emailId = Number.parseInt(req.params.id, 10);

    if (Number.isNaN(emailId)) {
      return sendError(res, ErrorCode.VALIDATION_ERROR, 'Invalid email ID');
    }

    let email = await emailService.getEmailById(userId, emailId);

    if (!email) {
      return sendError(res, ErrorCode.NOT_FOUND, 'Email not found', { emailId });
    }

    // Decrypt email content if it's encrypted
    if (email.isContentEncrypted) {
      try {
        email = await dataRetentionService.decryptEmailContent(email);
      } catch (error) {
        logger.error('Failed to decrypt email content', {
          emailId,
          userId,
          error: (error as Error).message,
        });
        // Continue with encrypted content rather than failing completely
      }
    }

    // Update last accessed timestamp for privacy tracking
    dataRetentionService.updateLastAccessed(emailId).catch(error => {
      logger.debug('Failed to update last accessed timestamp', {
        emailId,
        error: (error as Error).message,
      });
    });

    return sendSuccess(res, email, 'Email fetched successfully.');
  })
);

/**
 * POST /api/emails/:id/archive
 * Archive an email
 */
router.post(
  '/:id/archive',
  catchAsync(async (req: Request, res: Response) => {
    const userId = req.user!.id;
    const emailId = Number.parseInt(req.params.id, 10);

    if (Number.isNaN(emailId)) {
      return sendError(res, ErrorCode.VALIDATION_ERROR, 'Invalid email ID');
    }

    const success = await emailService.updateEmailState(userId, emailId, { isArchived: true });

    if (!success) {
      return sendError(res, ErrorCode.NOT_FOUND, 'Email not found or access denied', { emailId });
    }

    return sendSuccess(res, { archived: true }, 'Email archived successfully');
  })
);

/**
 * POST /api/emails/:id/trash
 * Move email to trash
 */
router.post(
  '/:id/trash',
  catchAsync(async (req: Request, res: Response) => {
    const userId = req.user!.id;
    const emailId = Number.parseInt(req.params.id, 10);

    if (Number.isNaN(emailId)) {
      return sendError(res, ErrorCode.VALIDATION_ERROR, 'Invalid email ID');
    }

    const updatedEmail = await emailService.updateEmailState(userId, emailId, { isTrashed: true });

    if (!updatedEmail) {
      return sendError(res, ErrorCode.NOT_FOUND, 'Email not found or access denied', { emailId });
    }

    return sendSuccess(res, { trashed: true }, 'Email moved to trash');
  })
);

/**
 * POST /api/emails/:id/important
 * Mark email as important
 */
router.post(
  '/:id/important',
  catchAsync(async (req: Request, res: Response) => {
    const userId = req.user!.id;
    const emailId = Number.parseInt(req.params.id, 10);
    const { important = true } = req.body;

    if (Number.isNaN(emailId)) {
      return sendError(res, ErrorCode.VALIDATION_ERROR, 'Invalid email ID');
    }

    const updatedEmail = await emailService.updateEmailState(userId, emailId, {
      isImportant: important,
    });

    if (!updatedEmail) {
      return sendError(res, ErrorCode.NOT_FOUND, 'Email not found or access denied', { emailId });
    }

    return sendSuccess(
      res,
      { important: important },
      important ? 'Email marked as important' : 'Email unmarked as important'
    );
  })
);

// Note: The /sync endpoint has complex logic involving the external Gmail API
// and AI processing. It remains in its current state pending a more in-depth
// refactoring of the `email-v2` service itself.

/**
 * POST /api/emails/sync
 * Sync emails from email providers
 */
router.post(
  '/sync',
  catchAsync(async (req: Request, res: Response) => {
    const userId = req.user!.id;

    // 1. Ensure user has valid Gmail tokens
    const user = req.user!;
    const accessToken = await tokenService.getValidAccessToken(user);

    if (!accessToken) {
      return sendError(
        res,
        ErrorCode.UNAUTHORIZED,
        'Gmail account is not connected or tokens are invalid. Please reconnect your account.'
      );
    }

    // 2. Fetch emails from Gmail and store them
    const fetchedEmails = await fetchGmailEmails(userId, accessToken);

    // Optionally enqueue AI processing for the newly fetched emails (Phase-2)
    const emailIds = fetchedEmails.map((e) => e.id).filter(Boolean) as number[];
    if (emailIds.length) {
      // Fire-and-forget – don't block the response
      processEmailsWithAI(userId, emailIds).catch((err) => {
        console.error('Failed to enqueue AI processing', err);
      });
    }

    return sendSuccess(res, { synced: fetchedEmails.length }, 'Emails synced successfully.');
  })
);

const createEmailSchema = EmailSchemas.create.omit({ userId: true });

/**
 * POST /api/emails
 * Create a new email record (primarily for testing or manual insertion).
 */
router.post(
  '/',
  validateRequest(createEmailSchema),
  catchAsync(async (req: Request, res: Response) => {
    const userId = req.user!.id;
    // The schema already validated body and ensured userId matches.
    const newEmail = await emailService.createEmail(userId, req.body);
    return sendSuccess(res, newEmail, 'Email created successfully.', 201);
  })
);

/**
 * PUT /api/emails/:id
 * Update an existing email (full update semantics).
 */
router.put(
  '/:id',
  validateRequest(EmailSchemas.update),
  catchAsync(async (req: Request, res: Response) => {
    const userId = req.user!.id;
    const emailId = Number.parseInt(req.params.id, 10);
    if (Number.isNaN(emailId)) {
      return sendError(res, ErrorCode.VALIDATION_ERROR, 'Invalid email ID');
    }
    const updated = await emailService.updateEmail(userId, emailId, req.body);
    if (!updated) {
      return sendError(res, ErrorCode.NOT_FOUND, 'Email not found or access denied', {
        emailId,
      });
    }
    return sendSuccess(res, updated, 'Email updated successfully.');
  })
);

/**
 * DELETE /api/emails/:id
 * Soft-delete an email (move to trash).
 */
router.delete(
  '/:id',
  catchAsync(async (req: Request, res: Response) => {
    const userId = req.user!.id;
    const emailId = Number.parseInt(req.params.id, 10);
    if (Number.isNaN(emailId)) {
      return sendError(res, ErrorCode.VALIDATION_ERROR, 'Invalid email ID');
    }

    const updated = await emailService.updateEmailState(userId, emailId, { isTrashed: true });
    if (!updated) {
      return sendError(res, ErrorCode.NOT_FOUND, 'Email not found or access denied', {
        emailId,
      });
    }
    return sendSuccess(res, { trashed: true }, 'Email deleted (trashed) successfully.');
  })
);

/**
 * POST /api/emails/process-ai
 * Manually trigger AI processing for emails with missing summaries
 */
router.post(
  '/process-ai',
  catchAsync(async (req: Request, res: Response) => {
    const userId = req.user!.id;
    const { emailIds, limit = 10 } = req.body;

    logger.info('Manual AI processing triggered', {
      userId,
      requestedEmailIds: emailIds?.length || 'all',
      limit
    });

    try {
      let targetEmailIds: number[] = [];

      if (emailIds && Array.isArray(emailIds)) {
        // Process specific email IDs
        targetEmailIds = emailIds.slice(0, limit);
      } else {
        // Find emails that need AI processing (missing summaries or have errors)
        const allEmails = await storage.getEmails(userId, limit * 3); // Get more to filter from
        const emailsNeedingAI = allEmails.filter((email: any) => 
          !email.summary || 
          email.summary === 'Error generating summary' ||
          email.summary === 'Summary unavailable' ||
          email.summary.trim() === ''
        );
        targetEmailIds = emailsNeedingAI.slice(0, limit).map((e: any) => e.id);
      }

      if (targetEmailIds.length === 0) {
        return sendSuccess(res, { 
          processed: 0, 
          message: 'No emails found that need AI processing' 
        });
      }

      logger.info('Starting manual AI processing', {
        userId,
        emailCount: targetEmailIds.length,
        emailIds: targetEmailIds
      });

      // Process emails with AI - don't await, let it run in background
      processEmailsWithAI(userId, targetEmailIds, limit).catch((err) => {
        logger.error('Manual AI processing failed', err, {
          userId,
          emailIds: targetEmailIds
        });
      });

      return sendSuccess(res, { 
        queued: targetEmailIds.length,
        emailIds: targetEmailIds,
        message: 'AI processing started in background'
      });

    } catch (error) {
      logger.error('Failed to trigger AI processing', error, {
        userId,
        requestedEmailIds: emailIds?.length || 'all'
      });
      return sendError(res, ErrorCode.INTERNAL_ERROR, 'Failed to trigger AI processing');
    }
  })
);

/**
 * POST /api/emails/summaries/fix
 * Fix all emails with error summaries by reprocessing them with AI
 */
router.post(
  '/summaries/fix',
  catchAsync(async (req: Request, res: Response) => {
    const userId = req.user!.id;
    const { limit = 20 } = req.body;

    try {
      // Get all emails for the user
      const allEmails = await storage.getEmails(userId, 100, 0);
      
      // Find emails with error summaries
      const errorEmails = allEmails.filter(email => 
        email.summary === 'Error generating summary' ||
        email.summary === 'Summary unavailable' ||
        email.summary === 'Processing...' ||
        !email.summary ||
        email.summary === '' ||
        (email.summary && (
          email.summary.toLowerCase().includes('error') ||
          email.summary.toLowerCase().includes('failed') ||
          email.summary.toLowerCase().includes('unavailable') ||
          email.summary.toLowerCase().includes('api limit') ||
          email.summary.toLowerCase().includes('could not')
        ))
      );

      if (errorEmails.length === 0) {
        return sendSuccess(res, { 
          successful: 0,
          failed: 0,
          remaining: 0,
          message: 'No emails with error summaries found'
        });
      }

      // Process up to the limit
      const emailsToFix = errorEmails.slice(0, limit);
      const emailIds = emailsToFix.map(email => email.id);

      logger.info('Fixing error summaries for emails', {
        userId,
        totalErrors: errorEmails.length,
        fixingCount: emailsToFix.length,
        emailIds
      });

      // Process emails with AI - don't await, let it run in background
      processEmailsWithAI(userId, emailIds, emailIds.length).catch((err) => {
        logger.error('Failed to fix error summaries', err, {
          userId,
          emailIds
        });
      });

      return sendSuccess(res, {
        successful: emailsToFix.length,
        failed: 0,
        remaining: Math.max(0, errorEmails.length - emailsToFix.length),
        message: `Started fixing ${emailsToFix.length} emails with error summaries`
      });

    } catch (error) {
      logger.error('Failed to fix error summaries', error, { userId });
      return sendError(res, ErrorCode.INTERNAL_ERROR, 'Failed to fix error summaries');
    }
  })
);

/**
 * GET /api/emails/diagnostic
 * Debug endpoint to check what emails are in the database and their dates
 */
router.get(
  '/diagnostic',
  catchAsync(async (req: Request, res: Response) => {
    const userId = req.user!.id;
    
    try {
      // Get the latest 10 emails to see what we have
      const emails = await storage.getEmails(userId, 10, 0);
      
      const emailInfo = emails.map(email => ({
        id: email.id,
        messageId: email.messageId,
        subject: email.subject?.substring(0, 60),
        sender: email.sender,
        receivedAt: email.receivedAt,
        receivedAtLocal: email.receivedAt ? new Date(email.receivedAt).toLocaleString() : null,
        isArchived: email.isArchived,
        summary: email.summary?.substring(0, 50)
      }));
      
      const now = new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);
      
      const todayEmails = emails.filter(email => 
        email.receivedAt && new Date(email.receivedAt) >= today
      ).length;
      
      const yesterdayEmails = emails.filter(email => 
        email.receivedAt && 
        new Date(email.receivedAt) >= yesterday && 
        new Date(email.receivedAt) < today
      ).length;
      
      return sendSuccess(res, {
        totalEmails: emails.length,
        todayEmails,
        yesterdayEmails,
        currentTime: now.toISOString(),
        currentTimeLocal: now.toLocaleString(),
        emails: emailInfo
      }, 'Diagnostic data retrieved');
      
    } catch (error) {
      logger.error('Failed to get diagnostic data', error, { userId });
      return sendError(res, ErrorCode.INTERNAL_ERROR, 'Failed to get diagnostic data');
    }
  })
);

/**
 * POST /api/emails/bulk-update
 * Phase 2: Bulk update multiple emails for improved performance
 */
router.post(
  '/bulk-update',
  validateRequest(z.object({
    updates: z.array(z.object({
      id: z.number().int().positive(),
      updates: z.object({
        isRead: z.boolean().optional(),
        isArchived: z.boolean().optional(),
        isImportant: z.boolean().optional(),
        isTrashed: z.boolean().optional(),
        categories: z.array(z.string()).optional(),
        priority: z.enum(['high', 'medium', 'low']).optional(),
      }).strict(),
    })).min(1).max(100), // Limit bulk operations to 100 emails
  })),
  catchAsync(async (req: Request, res: Response) => {
    const userId = req.user!.id;
    const { updates } = req.body;

    // Check if bulk operations are supported
    const dbStorage = storage as any;
    if (typeof dbStorage.bulkUpdateEmails !== 'function') {
      return sendError(res, ErrorCode.INTERNAL_ERROR, 'Bulk operations not supported');
    }

    try {
      const result = await dbStorage.bulkUpdateEmails(updates, userId);
      
      if (result.failed > 0) {
        logger.warn(`[BULK-UPDATE] Partial success: ${result.successful} successful, ${result.failed} failed`, {
          userId,
          totalUpdates: updates.length,
          errors: result.errors.slice(0, 5), // Log first 5 errors
        });
      }

      return sendSuccess(res, {
        successful: result.successful,
        failed: result.failed,
        errors: result.errors,
        totalProcessed: updates.length,
      }, `Bulk update completed: ${result.successful} successful, ${result.failed} failed`);
      
    } catch (error) {
      logger.error('[BULK-UPDATE] Failed to perform bulk update:', error, { userId, updateCount: updates.length });
      return sendError(res, ErrorCode.INTERNAL_ERROR, 'Failed to perform bulk update');
    }
  })
);

/**
 * POST /api/emails/bulk-archive
 * Phase 2: Bulk archive multiple emails
 */
router.post(
  '/bulk-archive',
  validateRequest(z.object({
    emailIds: z.array(z.number().int().positive()).min(1).max(100),
  })),
  catchAsync(async (req: Request, res: Response) => {
    const userId = req.user!.id;
    const { emailIds } = req.body;

    // Check if bulk operations are supported
    const dbStorage = storage as any;
    if (typeof dbStorage.bulkUpdateEmails !== 'function') {
      return sendError(res, ErrorCode.INTERNAL_ERROR, 'Bulk operations not supported');
    }

    try {
      // Convert to bulk update format
      const updates = emailIds.map((id: number) => ({
        id,
        updates: { isArchived: true },
      }));

      const result = await dbStorage.bulkUpdateEmails(updates, userId);
      
      return sendSuccess(res, {
        successful: result.successful,
        failed: result.failed,
        errors: result.errors,
        totalProcessed: emailIds.length,
      }, `Bulk archive completed: ${result.successful} emails archived`);
      
    } catch (error) {
      logger.error('[BULK-ARCHIVE] Failed to perform bulk archive:', error, { userId, emailCount: emailIds.length });
      return sendError(res, ErrorCode.INTERNAL_ERROR, 'Failed to perform bulk archive');
    }
  })
);

/**
 * POST /api/emails/bulk-mark-read
 * Phase 2: Bulk mark multiple emails as read
 */
router.post(
  '/bulk-mark-read',
  validateRequest(z.object({
    emailIds: z.array(z.number().int().positive()).min(1).max(100),
  })),
  catchAsync(async (req: Request, res: Response) => {
    const userId = req.user!.id;
    const { emailIds } = req.body;

    // Check if bulk operations are supported
    const dbStorage = storage as any;
    if (typeof dbStorage.bulkUpdateEmails !== 'function') {
      return sendError(res, ErrorCode.INTERNAL_ERROR, 'Bulk operations not supported');
    }

    try {
      // Convert to bulk update format
      const updates = emailIds.map((id: number) => ({
        id,
        updates: { isRead: true },
      }));

      const result = await dbStorage.bulkUpdateEmails(updates, userId);
      
      return sendSuccess(res, {
        successful: result.successful,
        failed: result.failed,
        errors: result.errors,
        totalProcessed: emailIds.length,
      }, `Bulk mark read completed: ${result.successful} emails marked as read`);
      
    } catch (error) {
      logger.error('[BULK-MARK-READ] Failed to perform bulk mark read:', error, { userId, emailCount: emailIds.length });
      return sendError(res, ErrorCode.INTERNAL_ERROR, 'Failed to perform bulk mark read');
    }
  })
);

/**
 * GET /api/emails/optimized
 * Phase 4: Get emails with advanced streaming and compression support
 */
router.get(
  '/optimized',
  validateRequest(z.object({
    limit: z.number().int().min(1).max(1000).optional().default(50),
    offset: z.number().int().min(0).optional().default(0),
    archived: z.boolean().optional(),
    important: z.boolean().optional(),
    snoozed: z.boolean().optional(),
    enableStreaming: z.boolean().optional().default(false),
    enableCompression: z.boolean().optional().default(true),
  })),
  catchAsync(async (req: Request, res: Response) => {
    const userId = req.user!.id;
    const { limit, offset, archived, important, snoozed, enableStreaming, enableCompression } = req.query;

    // Check if optimized operations are supported
    const dbStorage = storage as any;
    if (typeof dbStorage.getEmailsOptimized !== 'function') {
      // Fallback to regular getEmails
      const emails = await storage.getEmails(userId, Number(limit), Number(offset), {
        archived: archived === 'true' ? true : archived === 'false' ? false : undefined,
        important: important === 'true' ? true : undefined,
        snoozed: snoozed === 'true' ? true : undefined,
      });
      
      return sendSuccess(res, { 
        emails, 
        compressionResult: null,
        fallback: true,
        message: 'Using fallback method - optimized operations not available'
      });
    }

    try {
      const filters = {
        archived: archived === 'true' ? true : archived === 'false' ? false : undefined,
        important: important === 'true' ? true : undefined,
        snoozed: snoozed === 'true' ? true : undefined,
      };

      const options = {
        enableStreaming: enableStreaming === 'true',
        enableCompression: enableCompression === 'true',
      };

      const result = await dbStorage.getEmailsOptimized(
        userId, 
        Number(limit), 
        Number(offset), 
        filters, 
        options
      );
      
      const response = {
        emails: result.emails,
        count: result.emails.length,
        compressionResult: result.compressionResult ? {
          originalSize: `${Math.round(result.compressionResult.originalSize / 1024)}KB`,
          compressedSize: `${Math.round(result.compressionResult.compressedSize / 1024)}KB`,
          compressionRatio: `${(result.compressionResult.compressionRatio * 100).toFixed(1)}%`,
          compressionTime: `${result.compressionResult.compressionTime}ms`,
        } : null,
        optimizations: {
          streamingEnabled: options.enableStreaming,
          compressionEnabled: options.enableCompression,
          compressionApplied: !!result.compressionResult,
        }
      };

      return sendSuccess(res, response, 'Optimized emails retrieved successfully');
      
    } catch (error) {
      logger.error('[OPTIMIZED-EMAILS] Failed to get optimized emails:', error, { userId });
      return sendError(res, ErrorCode.INTERNAL_ERROR, 'Failed to get optimized emails');
    }
  })
);

/**
 * GET /api/emails/stream
 * Phase 4: Stream large email datasets
 */
router.get(
  '/stream',
  validateRequest(z.object({
    archived: z.boolean().optional(),
    important: z.boolean().optional(),
    snoozed: z.boolean().optional(),
    chunkSize: z.number().int().min(10).max(500).optional().default(100),
  })),
  catchAsync(async (req: Request, res: Response) => {
    const userId = req.user!.id;
    const { archived, important, snoozed, chunkSize } = req.query;

    // Check if streaming is supported
    const dbStorage = storage as any;
    if (typeof dbStorage.streamEmails !== 'function') {
      return sendError(res, ErrorCode.INTERNAL_ERROR, 'Email streaming not supported');
    }

    try {
      const filters = {
        archived: archived === 'true' ? true : archived === 'false' ? false : undefined,
        important: important === 'true' ? true : undefined,
        snoozed: snoozed === 'true' ? true : undefined,
      };

      // Set up Server-Sent Events
      res.writeHead(200, {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Cache-Control',
      });

      let chunkCount = 0;
      let totalEmails = 0;

      // Send initial message
      res.write(`data: ${JSON.stringify({ 
        type: 'start', 
        message: 'Starting email stream',
        chunkSize: Number(chunkSize)
      })}\n\n`);

      const stream = dbStorage.streamEmails(userId, filters);
      
      for await (const chunk of stream) {
        chunkCount++;
        totalEmails += chunk.length;
        
        const data = {
          type: 'chunk',
          chunkNumber: chunkCount,
          emails: chunk,
          chunkSize: chunk.length,
          totalProcessed: totalEmails,
        };
        
        res.write(`data: ${JSON.stringify(data)}\n\n`);
        
        // Respect chunk size limit
        if (chunk.length < Number(chunkSize)) {
          break; // End of data
        }
      }

      // Send completion message
      res.write(`data: ${JSON.stringify({ 
        type: 'complete', 
        totalChunks: chunkCount,
        totalEmails,
        message: 'Email stream completed'
      })}\n\n`);

      res.end();
      
    } catch (error) {
      logger.error('[STREAM-EMAILS] Failed to stream emails:', error, { userId });
      
      // Send error message
      res.write(`data: ${JSON.stringify({ 
        type: 'error', 
        message: 'Failed to stream emails',
        error: error instanceof Error ? error.message : String(error)
      })}\n\n`);
      
      res.end();
    }
  })
);

/**
 * GET /api/emails/advanced-metrics
 * Phase 4: Get advanced performance metrics for email operations
 */
router.get(
  '/advanced-metrics',
  catchAsync(async (req: Request, res: Response) => {
    const userId = req.user!.id;

    // Check if advanced metrics are supported
    const dbStorage = storage as any;
    if (typeof dbStorage.getAdvancedMetrics !== 'function') {
      return sendError(res, ErrorCode.INTERNAL_ERROR, 'Advanced metrics not supported');
    }

    try {
      const metrics = dbStorage.getAdvancedMetrics();
      
      const response = {
        queryPerformance: {
          totalQueries: metrics.queryMetrics.totalQueries,
          cachedQueries: metrics.queryMetrics.cachedQueries,
          streamedQueries: metrics.queryMetrics.streamedQueries,
          compressedResponses: metrics.queryMetrics.compressedResponses,
          cacheWarmingOperations: metrics.queryMetrics.cacheWarmingOperations,
          averageQueryTime: `${Math.round(metrics.queryMetrics.averageQueryTime)}ms`,
          cacheHitRate: `${((metrics.queryMetrics.cachedQueries / Math.max(metrics.queryMetrics.totalQueries, 1)) * 100).toFixed(1)}%`,
          compressionRatio: `${(metrics.queryMetrics.compressionRatio * 100).toFixed(1)}%`,
        },
        streamingConfiguration: {
          chunkSize: metrics.streamingOptions.chunkSize,
          compressionEnabled: metrics.streamingOptions.enableCompression,
          compressionThreshold: `${Math.round(metrics.streamingOptions.compressionThreshold / 1024)}KB`,
        },
        cacheWarming: {
          enabled: metrics.cacheWarmingConfig.enabled,
          interval: `${Math.round(metrics.cacheWarmingConfig.warmingInterval / 1000)}s`,
          maxWarmingTime: `${Math.round(metrics.cacheWarmingConfig.maxWarmingTime / 1000)}s`,
          warmingQueries: metrics.cacheWarmingConfig.warmingQueries,
        },
        cacheStatistics: {
          size: metrics.cacheStats.size,
          hitRate: `${(metrics.cacheStats.hitRate * 100).toFixed(1)}%`,
          memoryUsage: metrics.cacheStats.memoryUsage,
        },
        recommendations: [
          ...(metrics.queryMetrics.averageQueryTime > 500 ? ['Consider optimizing slow queries'] : []),
          ...(metrics.cacheStats.hitRate < 0.5 ? ['Cache hit rate is low - consider increasing cache TTL'] : []),
          ...(metrics.queryMetrics.compressionRatio < 0.3 ? ['Compression ratio is low - review compression threshold'] : []),
          ...(metrics.cacheStats.size > 800 ? ['Cache size is high - consider reducing cache size or TTL'] : []),
        ],
      };

      return sendSuccess(res, response, 'Advanced metrics retrieved successfully');
      
    } catch (error) {
      logger.error('[ADVANCED-METRICS] Failed to get advanced metrics:', error, { userId });
      return sendError(res, ErrorCode.INTERNAL_ERROR, 'Failed to get advanced metrics');
    }
  })
);

/**
 * POST /api/emails/optimization-settings
 * Phase 4: Update streaming and compression settings
 */
router.post(
  '/optimization-settings',
  validateRequest(z.object({
    streamingOptions: z.object({
      chunkSize: z.number().int().min(10).max(500).optional(),
      enableCompression: z.boolean().optional(),
      compressionThreshold: z.number().int().min(1024).optional(),
    }).optional(),
    cacheWarmingConfig: z.object({
      enabled: z.boolean().optional(),
      warmingInterval: z.number().int().min(60000).optional(), // Minimum 1 minute
      maxWarmingTime: z.number().int().min(5000).optional(), // Minimum 5 seconds
    }).optional(),
  })),
  catchAsync(async (req: Request, res: Response) => {
    const userId = req.user!.id;
    const { streamingOptions, cacheWarmingConfig } = req.body;

    // Check if optimization settings are supported
    const dbStorage = storage as any;
    if (typeof dbStorage.updateOptimizationSettings !== 'function') {
      return sendError(res, ErrorCode.INTERNAL_ERROR, 'Optimization settings not supported');
    }

    try {
      dbStorage.updateOptimizationSettings({
        streamingOptions,
        cacheWarmingConfig,
      });
      
      // Get updated settings
      const updatedMetrics = dbStorage.getAdvancedMetrics();
      
      return sendSuccess(res, {
        streamingOptions: updatedMetrics.streamingOptions,
        cacheWarmingConfig: updatedMetrics.cacheWarmingConfig,
        message: 'Optimization settings updated successfully'
      });
      
    } catch (error) {
      logger.error('[OPTIMIZATION-SETTINGS] Failed to update optimization settings:', error, { userId });
      return sendError(res, ErrorCode.INTERNAL_ERROR, 'Failed to update optimization settings');
    }
  })
);

export default router;
