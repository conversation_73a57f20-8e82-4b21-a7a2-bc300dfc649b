import { findOrCreateUser, type ProviderProfile } from '../../../server/auth/user.service';
import logger from '../../../server/lib/logger';
import { storage } from '../../../server/storage';
import type { User } from '../../../shared/schema';

// Mock the dependencies
jest.mock('../../../server/storage', () => ({
  storage: {
    getUserByEmail: jest.fn(),
    updateUser: jest.fn(),
    createUser: jest.fn(),
    getUserById: jest.fn(),
  },
}));
jest.mock('../../../server/lib/logger');

describe('User Service: findOrCreateUser', () => {
  const mockedStorage = storage as jest.Mocked<typeof storage>;
  const mockedLogger = logger as jest.Mocked<typeof logger>;

  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();
  });

  const mockProfile: ProviderProfile = {
    email: '<EMAIL>',
    name: 'Test User',
    picture: 'http://example.com/picture.jpg',
    provider: 'google',
    tokens: {
      access_token: 'test_access_token',
      refresh_token: 'test_refresh_token',
      expires_in: 3599,
    },
  };

  it('should find an existing user and update their details and tokens', async () => {
    const existingUser: User = {
      id: 1,
      email: '<EMAIL>',
      name: 'Old Name',
      role: 'user',
    };
    const expectedUpdatedUser = {
      ...existingUser,
      name: mockProfile.name,
      picture: mockProfile.picture,
    };

    mockedStorage.getUserByEmail.mockResolvedValue(existingUser);
    mockedStorage.updateUser.mockResolvedValue(expectedUpdatedUser);

    const result = await findOrCreateUser(mockProfile);

    expect(mockedStorage.getUserByEmail).toHaveBeenCalledWith(mockProfile.email);
    expect(mockedStorage.updateUser).toHaveBeenCalledWith(
      existingUser.id,
      expect.objectContaining({
        name: mockProfile.name,
        picture: mockProfile.picture,
        gmailTokens: JSON.stringify(mockProfile.tokens),
        lastLogin: expect.any(Date),
        provider: mockProfile.provider,
      })
    );
    expect(mockedStorage.createUser).not.toHaveBeenCalled();
    expect(result).toEqual(expectedUpdatedUser);
    expect(mockedLogger.info).toHaveBeenCalledWith('Existing user found, updating details.', {
      userId: existingUser.id,
      provider: mockProfile.provider,
    });
  });

  it('should link a new provider to an existing logged-in user', async () => {
    const loggedInUser: User = {
      id: 5,
      email: '<EMAIL>',
      name: 'Original User',
      provider: 'firebase',
      role: 'user',
    };
    const profileWithExistingId: ProviderProfile = {
      ...mockProfile,
      existingUserId: loggedInUser.id,
    };
    const expectedUpdatedUser = {
      ...loggedInUser,
      name: profileWithExistingId.name,
      picture: profileWithExistingId.picture,
    };

    // Mock the new path: finding the user by their existing session ID
    mockedStorage.getUserById.mockResolvedValue(loggedInUser);
    mockedStorage.updateUser.mockResolvedValue(expectedUpdatedUser);

    const result = await findOrCreateUser(profileWithExistingId);

    // Verify the correct functions were called for the "linking" flow
    expect(mockedStorage.getUserById).toHaveBeenCalledWith(loggedInUser.id);
    expect(mockedStorage.updateUser).toHaveBeenCalledWith(
      loggedInUser.id,
      expect.objectContaining({
        gmailTokens: JSON.stringify(profileWithExistingId.tokens),
      })
    );

    // Verify the old functions were NOT called
    expect(mockedStorage.getUserByEmail).not.toHaveBeenCalled();
    expect(mockedStorage.createUser).not.toHaveBeenCalled();

    expect(result).toEqual(expectedUpdatedUser);
    expect(mockedLogger.info).toHaveBeenCalledWith('Linking new provider to existing logged-in user.', {
      userId: loggedInUser.id,
      provider: profileWithExistingId.provider,
    });
  });

  it('should find an existing user by email and update their details and tokens', async () => {
    const existingUser: User = {
      id: 1,
      email: '<EMAIL>',
      name: 'Old Name',
      role: 'user',
    };
    const expectedUpdatedUser = {
      ...existingUser,
      name: mockProfile.name,
      picture: mockProfile.picture,
    };

    mockedStorage.getUserByEmail.mockResolvedValue(existingUser);
    mockedStorage.updateUser.mockResolvedValue(expectedUpdatedUser);

    const result = await findOrCreateUser(mockProfile);

    expect(mockedStorage.getUserByEmail).toHaveBeenCalledWith(mockProfile.email);
    expect(mockedStorage.updateUser).toHaveBeenCalledWith(
      existingUser.id,
      expect.objectContaining({
        name: mockProfile.name,
        picture: mockProfile.picture,
        gmailTokens: JSON.stringify(mockProfile.tokens),
        lastLogin: expect.any(Date),
        provider: mockProfile.provider,
      })
    );
    expect(mockedStorage.createUser).not.toHaveBeenCalled();
    expect(result).toEqual(expectedUpdatedUser);
    expect(mockedLogger.info).toHaveBeenCalledWith('Existing user found by email, updating details.', {
      userId: existingUser.id,
      provider: mockProfile.provider,
    });
  });

  it('should create a new user if one does not exist', async () => {
    const newUser: User = {
      id: 2,
      email: mockProfile.email,
      name: mockProfile.name || '',
      role: 'user',
    };

    mockedStorage.getUserByEmail.mockResolvedValue(null);
    mockedStorage.createUser.mockResolvedValue(newUser);

    const result = await findOrCreateUser(mockProfile);

    expect(mockedStorage.getUserByEmail).toHaveBeenCalledWith(mockProfile.email);
    expect(mockedStorage.createUser).toHaveBeenCalledWith(
      expect.objectContaining({
        email: mockProfile.email,
        name: mockProfile.name,
        provider: mockProfile.provider,
        gmailTokens: JSON.stringify(mockProfile.tokens),
        tier: 'free',
        lastLogin: expect.any(Date),
      })
    );
    expect(mockedStorage.updateUser).not.toHaveBeenCalled();
    expect(result).toEqual(newUser);
    expect(mockedLogger.info).toHaveBeenCalledWith('New user detected, creating account.', {
      email: mockProfile.email,
      provider: mockProfile.provider,
    });
  });

  it('should throw an error if the profile does not contain an email', async () => {
    const profileWithoutEmail: ProviderProfile = { ...mockProfile, email: undefined };

    await expect(findOrCreateUser(profileWithoutEmail)).rejects.toThrow(
      'Cannot find or create a user without an email address.'
    );
  });

  it('should stringify tokens into gmailTokens field when creating a new user', async () => {
    mockedStorage.getUserByEmail.mockResolvedValue(null);
    mockedStorage.createUser.mockResolvedValue({
      id: 3,
      email: mockProfile.email,
      name: mockProfile.name || '',
      role: 'user',
    } as unknown as User);

    await findOrCreateUser(mockProfile);

    expect(mockedStorage.createUser).toHaveBeenCalledWith(
      expect.objectContaining({
        gmailTokens: JSON.stringify(mockProfile.tokens),
      })
    );
  });
});
