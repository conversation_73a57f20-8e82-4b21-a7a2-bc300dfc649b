import {
  blendColors,
  cn,
  getCssVariable,
  getThemeAdjustedColor,
  getUserInitials,
  hexToRGB,
  isDarkMode,
} from '@/lib/utils';

// Test suite for the `cn` utility function which merges Tailwind classes.
describe('cn (Class Name Utility)', () => {
  // Test case for combining multiple class strings.
  it('should merge multiple class names correctly', () => {
    expect(cn('bg-red-500', 'text-white')).toBe('bg-red-500 text-white');
  });

  // Test case for handling conditional classes with some being falsy.
  it('should handle conditional classes', () => {
    const hasBorder = false;
    const hasPadding = true;
    expect(cn('base-class', hasBorder && 'border', hasPadding && 'p-4')).toBe('base-class p-4');
  });

  // Test case to ensure conflicting Tailwind classes are resolved correctly.
  it('should merge conflicting tailwind classes by taking the last one', () => {
    expect(cn('p-2', 'p-4')).toBe('p-4');
    expect(cn('bg-red-500', 'bg-blue-500')).toBe('bg-blue-500');
  });

  // Test case for mixing strings, objects, and arrays as input.
  it('should handle a mix of different types of arguments', () => {
    expect(cn('font-bold', { 'text-center': true }, ['flex', 'items-center'])).toBe(
      'font-bold text-center flex items-center'
    );
  });

  // Test case for handling `null`, `undefined`, and boolean `false` values.
  it('should ignore falsy values', () => {
    expect(cn('class-a', null, 'class-b', undefined, false, 'class-c')).toBe(
      'class-a class-b class-c'
    );
  });
});

// Test suite for the `hexToRGB` utility function.
describe('hexToRGB', () => {
  // Test converting a valid hex color to its RGB equivalent.
  it('should convert a valid hex color to an RGB string', () => {
    expect(hexToRGB('#ffffff')).toBe('rgb(255, 255, 255)');
    expect(hexToRGB('#000000')).toBe('rgb(0, 0, 0)');
    expect(hexToRGB('#ff0000')).toBe('rgb(255, 0, 0)');
  });

  // Test converting a valid hex color to RGBA with a specified alpha.
  it('should convert a valid hex color to an RGBA string with alpha', () => {
    expect(hexToRGB('#ffffff', 0.5)).toBe('rgba(255, 255, 255, 0.5)');
    expect(hexToRGB('#000000', 0)).toBe('rgba(0, 0, 0, 0)');
  });

  // Test that invalid hex codes return a default fallback color.
  it('should return a default fallback color for invalid hex codes', () => {
    expect(hexToRGB('invalid')).toBe('rgb(128, 128, 128)'); // Not a hex
    expect(hexToRGB('#123')).toBe('rgb(128, 128, 128)'); // Too short
    expect(hexToRGB('#gggggg')).toBe('rgb(128, 128, 128)'); // Invalid characters
  });

  // Test that invalid hex codes with alpha return a default RGBA fallback.
  it('should return a default RGBA fallback for invalid hex with alpha', () => {
    expect(hexToRGB('invalid', 0.7)).toBe('rgba(128, 128, 128, 0.7)');
  });
});

// Test suite for the `getUserInitials` utility function.
describe('getUserInitials', () => {
  // Test with a standard full name.
  it('should return the first letter of each part of a name', () => {
    expect(getUserInitials('John Doe')).toBe('JD');
  });

  // Test with a single name.
  it('should return the first letter of a single name', () => {
    expect(getUserInitials('John')).toBe('J');
  });

  // Test with a name containing multiple spaces.
  it('should handle multiple spaces between names', () => {
    expect(getUserInitials('John  Doe')).toBe('JD');
  });

  // Test with an empty string.
  it('should return an empty string if the name is empty', () => {
    expect(getUserInitials('')).toBe('');
  });

  // Test to ensure the output is always uppercase.
  it('should return uppercase initials', () => {
    expect(getUserInitials('jane doe')).toBe('JD');
  });
});

// Test suite for `getCssVariable` which requires a DOM environment.
describe('getCssVariable', () => {
  beforeEach(() => {
    // Mock getComputedStyle to simulate CSS variables
    Object.defineProperty(window, 'getComputedStyle', {
      value: () => ({
        getPropertyValue: (prop) => {
          if (prop === '--primary') return ' #ff0000 '; // Note the spaces to test trimming
          if (prop === '--secondary-color') return '#0000ff';
          return '';
        },
      }),
    });
  });

  it('should return the value of an existing CSS variable', () => {
    expect(getCssVariable('--primary')).toBe('#ff0000');
    expect(getCssVariable('--secondary-color')).toBe('#0000ff');
  });

  it('should return the fallback value for a non-existent CSS variable', () => {
    expect(getCssVariable('--non-existent', '#000000')).toBe('#000000');
  });

  it('should return an empty string if no fallback is provided for a non-existent variable', () => {
    expect(getCssVariable('--non-existent')).toBe('');
  });
});

// Test suite for `isDarkMode` which depends on document classes and matchMedia.
describe('isDarkMode', () => {
  const mockMatchMedia = (matches) => {
    Object.defineProperty(window, 'matchMedia', {
      writable: true,
      value: jest.fn().mockImplementation((query) => ({
        matches,
        media: query,
        onchange: null,
        addListener: jest.fn(),
        removeListener: jest.fn(),
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
        dispatchEvent: jest.fn(),
      })),
    });
  };

  afterEach(() => {
    // Clean up class list after each test
    document.documentElement.className = '';
  });

  it('should return true if the dark class is present', () => {
    document.documentElement.className = 'dark';
    expect(isDarkMode()).toBe(true);
  });

  it('should return false if the light class is present', () => {
    document.documentElement.className = 'light';
    expect(isDarkMode()).toBe(false);
  });

  it('should return true if system preference is dark', () => {
    mockMatchMedia(true);
    document.documentElement.className = 'system';
    expect(isDarkMode()).toBe(true);
  });

  it('should return false if system preference is light', () => {
    mockMatchMedia(false);
    document.documentElement.className = 'system';
    expect(isDarkMode()).toBe(false);
  });

  it('should default to system preference if no theme class is set', () => {
    mockMatchMedia(true);
    expect(isDarkMode()).toBe(true);
    mockMatchMedia(false);
    expect(isDarkMode()).toBe(false);
  });
});

// Test suite for the `blendColors` utility function.
describe('blendColors', () => {
  const BLACK = '#000000';
  const WHITE = '#ffffff';

  it('should correctly blend two colors at 50%', () => {
    // Blending black and white at 50% should result in gray.
    expect(blendColors(BLACK, WHITE, 50)).toBe('#808080');
  });

  it('should return the first color when percentage is 0', () => {
    expect(blendColors(BLACK, WHITE, 0)).toBe(BLACK);
  });

  it('should return the second color when percentage is 100', () => {
    expect(blendColors(BLACK, WHITE, 100)).toBe(WHITE);
  });

  it('should return a fallback color for invalid hex inputs', () => {
    expect(blendColors('invalid', WHITE, 50)).toBe('#888888');
    expect(blendColors(BLACK, '#123', 50)).toBe('#888888');
  });
});

// Test suite for the `getThemeAdjustedColor` utility function.
describe('getThemeAdjustedColor', () => {
  it('should lighten a dark color in dark mode', () => {
    const darkColor = '#1a202c'; // A dark gray
    const adjusted = getThemeAdjustedColor(darkColor, true);
    expect(adjusted).not.toBe(darkColor);
    // Expect the adjusted color to be brighter (higher hex value)
    expect(Number.parseInt(adjusted.slice(1), 16)).toBeGreaterThan(
      Number.parseInt(darkColor.slice(1), 16)
    );
  });

  it('should darken a light color in light mode', () => {
    const lightColor = '#edf2f7'; // A very light gray
    const adjusted = getThemeAdjustedColor(lightColor, false);
    expect(adjusted).not.toBe(lightColor);
    // Expect the adjusted color to be darker (lower hex value)
    expect(Number.parseInt(adjusted.slice(1), 16)).toBeLessThan(
      Number.parseInt(lightColor.slice(1), 16)
    );
  });

  it('should not adjust a color that has enough contrast in dark mode', () => {
    // This color has a calculated brightness of > 130, so it should not be changed.
    const brightColor = '#63b3ed';
    expect(getThemeAdjustedColor(brightColor, true)).toBe(brightColor);
  });

  it('should not adjust a color that has enough contrast in light mode', () => {
    const darkColor = '#2d3748'; // A sufficiently dark gray
    expect(getThemeAdjustedColor(darkColor, false)).toBe(darkColor);
  });

  it('should return a fallback for invalid color input', () => {
    expect(getThemeAdjustedColor('invalid', true)).toBe('#c0c0c0');
    expect(getThemeAdjustedColor('invalid', false)).toBe('#505050');
  });
});

// Note: Tests for `getCssVariable`, `isDarkMode`, `blendColors`, and `getThemeAdjustedColor`
// are omitted here as they require a DOM environment (for `window`/`document`) or have more complex dependencies.
// They can be tested effectively in an environment like Jest with `jsdom` configured.
