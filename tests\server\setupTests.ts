// Patch the CommonJS `express` module to ensure it has a `default` export when running in Jest
// This mirrors the behavior that TypeScript expects when using `import express from 'express'`
// in test sources compiled by ts-jest. Without this patch, calls to `express()` may fail with
// "TypeError: express_default is not a function" because `express.default` is undefined.
const express = require('express');

// If the "default" export is missing, alias the module itself.
if (!express.default) {
  // eslint-disable-next-line no-param-reassign
  express.default = express;
}

const session = require('express-session');
if (!session.default) {
  // eslint-disable-next-line no-param-reassign
  session.default = session;
}

// Provide a lightweight mock for the Winston-based logger to avoid heavy initialisation and
// to prevent TypeErrors when the EnvironmentValidator expects logger methods.
jest.mock('server/lib/logger', () => ({
  __esModule: true,
  default: {
    debug: jest.fn(),
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
    fatal: jest.fn(),
  },
})); 