import { RefreshCcw } from 'lucide-react';
import type React from 'react';
import type { ReactNode } from 'react';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { useEmailDetail } from '@/context/EmailDetailContext';
import type { Email } from '@/types/email';
import EmailItem from './EmailItem';

interface SimpleEmailListProps {
  emails: Email[];
  isLoading: boolean;
  emptyStateIcon?: ReactNode;
  emptyStateTitle?: string;
  emptyStateDescription?: string;
  onRefresh?: () => void;
}

const SimpleEmailList: React.FC<SimpleEmailListProps> = ({
  emails,
  isLoading,
  emptyStateIcon,
  emptyStateTitle = 'No emails',
  emptyStateDescription = 'Your inbox is empty',
  onRefresh,
}) => {
  const { selectedEmail, selectEmail } = useEmailDetail();
  const skeletonKeys = ['s1', 's2', 's3', 's4', 's5'];

  return (
    <div className="flex-1 overflow-y-auto">
      {isLoading ? (
        // Loading skeleton
        skeletonKeys.map((key) => (
          <div key={key} className="border-b border-gray-200 p-3">
            <div className="flex justify-between items-start mb-1">
              <Skeleton className="h-4 sm:h-5 w-1/3" />
              <Skeleton className="h-3 sm:h-4 w-16" />
            </div>
            <div className="flex space-x-2 mb-1">
              <Skeleton className="h-4 sm:h-5 w-16 rounded" />
            </div>
            <Skeleton className="h-4 sm:h-5 w-4/5 mb-1" />
            <Skeleton className="h-3 sm:h-4 w-full mb-1" />
            <Skeleton className="h-3 sm:h-4 w-full mb-1" />
          </div>
        ))
      ) : emails.length === 0 ? (
        // Empty state
        <div className="flex-1 flex items-center justify-center p-4 sm:p-6">
          <div className="text-center">
            <div className="flex justify-center mb-4">{emptyStateIcon}</div>
            <h3 className="text-base sm:text-lg font-medium text-gray-900 mb-1">
              {emptyStateTitle}
            </h3>
            <p className="text-xs sm:text-sm text-gray-500">{emptyStateDescription}</p>
            {onRefresh && (
              <div className="mt-3">
                <Button variant="outline" size="default" onClick={onRefresh}>
                  <RefreshCcw className="h-4 w-4 mr-2" />
                  Refresh
                </Button>
              </div>
            )}
          </div>
        </div>
      ) : (
        // Standard email list
        emails.map((email) => (
          <EmailItem
            key={email.id}
            email={email}
            isSelected={selectedEmail?.id === email.id}
            onClick={() => selectEmail(email.id)}
          />
        ))
      )}
    </div>
  );
};

export default SimpleEmailList;
