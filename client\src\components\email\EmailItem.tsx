import { format } from 'date-fns';
import { motion } from 'framer-motion';
import type React from 'react';
import { memo, useMemo } from 'react';
import { Avatar } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import CategoryBadge from '@/components/ui/badge-category';
import { htmlToPlainText } from '@/utils/emailContentParser';
import { normalizeCategories } from '@/lib/emailFilters';
import type { Email } from '@/types/email';
import UrgencyBadge from './UrgencyBadge';

interface EmailItemProps {
  email: Email;
  isSelected: boolean;
  onClick: () => void;
}

// Memoized HTML content stripper function
// Moved outside component to prevent recreation on each render
const useStrippedHtmlContent = (htmlContent: string | undefined, length = 150): string => {
  return useMemo(() => {
    if (!htmlContent) return '';

    try {
      // Use the safe utility function to convert HTML to plain text.
      const text = htmlToPlainText(htmlContent);
      return text.substring(0, length);
    } catch (error) {
      console.error('Error stripping HTML:', error);
      return 'Error processing content';
    }
  }, [htmlContent, length]);
};

// Content preview component - memoized to prevent unnecessary rerenders
const EmailPreview = memo(
  ({
    summary,
    snippet,
    htmlContent,
    originalContent,
  }: {
    summary: string | null | undefined;
    snippet: string | null | undefined;
    htmlContent: string | null | undefined;
    originalContent: string | null | undefined;
  }) => {
    // Memoize the potentially expensive HTML stripping operation
    const strippedHtml = useStrippedHtmlContent(htmlContent || '', 150);
    const contentPreview = useMemo(() => {
      if (summary === 'Processing...') {
        return (
          <div className="flex items-center">
            <span className="status-indicator status-processing mr-1.5" />
            <span className="italic text-warning">Generating summary...</span>
          </div>
        );
      }
      if (summary === 'Error generating summary') {
        return (
          <span className="italic text-destructive">
            Error generating summary. Click "Fix Summaries" to retry.
          </span>
        );
      }
      if (summary === 'Summary unavailable') {
        return (
          <span className="italic">
            {snippet ||
              strippedHtml ||
              originalContent?.substring(0, 150) ||
              'No content available'}
          </span>
        );
      }
      if (summary) {
        // Valid summary
        return summary;
      }
      // Fallback to snippet or content preview
      return (
        snippet || strippedHtml || originalContent?.substring(0, 150) || 'No content available'
      );
    }, [summary, snippet, strippedHtml, originalContent]);

    // Apply strict overflow control wrapper around content
    return (
      <div className="text-xs text-muted-foreground mb-2 email-summary overflow-hidden">
        <div className="email-summary-inner">{contentPreview}</div>
      </div>
    );
  }
);
EmailPreview.displayName = 'EmailPreview';

// Category badges component - memoized to prevent unnecessary rerenders
const CategoryBadges = memo(({ categories }: { categories: string[] }) => {
  // Filter and count non-urgent categories once
  const nonUrgentCategories = useMemo(
    () => categories.filter((cat) => cat !== 'Urgent'),
    [categories]
  );

  // Only show up to 2 categories plus a count badge if needed
  return (
    <>
      {nonUrgentCategories.length > 0 &&
        nonUrgentCategories
          .slice(0, 2)
          .map((category: string) => (
            <CategoryBadge key={category} category={category} className="text-xs" />
          ))}

      {/* Show "+N" if we have more than 2 non-Urgent categories */}
      {nonUrgentCategories.length > 2 && (
        <Badge variant="secondary" className="text-xs">
          +{nonUrgentCategories.length - 2}
        </Badge>
      )}
    </>
  );
});
CategoryBadges.displayName = 'CategoryBadges';

// Motion configuration constants - moved outside component to prevent recreation
const MOTION_CONFIG = {
  layout: true,
  initial: { opacity: 0, y: 10 },
  animate: { opacity: 1, y: 0 },
  exit: { opacity: 0, y: -10 },
  transition: { duration: 0.2 },
  whileHover: { backgroundColor: 'var(--accent-darker)', scale: 1.005 },
} as const;

const EmailItem: React.FC<EmailItemProps> = ({ email, isSelected, onClick }) => {
  // Remove problematic performance monitoring that was causing infinite loops
  // Performance monitoring will be handled at a higher level if needed

  // Memoize date formatting to avoid recomputing on every render
  const formattedDate = useMemo(() => {
    return email.receivedAt ? format(new Date(email.receivedAt), 'MMM d, h:mm a') : 'Unknown date';
  }, [email.receivedAt]);

  // Memoize sender initial to avoid recomputing
  const senderInitial = useMemo(() => {
    return email.sender ? email.sender.charAt(0).toUpperCase() : '?';
  }, [email.sender]);

  // Memoize category parsing to avoid expensive operations on every render
  const categories = useMemo(() => normalizeCategories((email as any).categories), [email.categories]);

  // Memoize read status - safely check if 'read' or 'isRead' property exists
  const isRead = useMemo(() => {
    // Check multiple potential property names for read status
    if ('read' in email && typeof (email as { read: unknown }).read === 'boolean') {
      return (email as { read: boolean }).read;
    }
    if ('isRead' in email && typeof (email as { isRead: unknown }).isRead === 'boolean') {
      return (email as { isRead: boolean }).isRead;
    }
    return false;
  }, [email]);

  // Memoize className calculation
  const itemClassName = useMemo(
    () => `email-item ${isSelected ? 'selected' : ''} ${isRead ? 'read' : 'unread'}`,
    [isSelected, isRead]
  );

  return (
    <div
      className={itemClassName}
      onClick={onClick}
      data-testid="email-item"
    >
      <div className="flex items-start gap-3">
        <Avatar className="h-10 w-10 flex-shrink-0 border border-border shadow-sm">
          <div className="flex h-full w-full items-center justify-center bg-primary text-primary-foreground">
            {senderInitial}
          </div>
        </Avatar>

        <div className="flex-1 min-w-0">
          <div className="flex justify-between items-start mb-1 gap-1">
            <div className="text-sm font-medium truncate max-w-[70%] flex items-center">
              {/* Restore the unread dot next to the sender name */}
              {!isRead && (
                <span
                  className="h-2 w-2 bg-primary rounded-full mr-2 flex-shrink-0"
                  title="Unread email"
                />
              )}
              {email.sender || 'Unknown sender'}
            </div>
            <div className="text-xs text-muted-foreground whitespace-nowrap flex-shrink-0">
              {formattedDate}
            </div>
          </div>

          <div className="text-sm font-medium mb-1 truncate">
            {email.subject || '(No subject)'}
          </div>

          {/* Use our memoized EmailPreview component */}
          <EmailPreview
            summary={email.summary}
            snippet={email.snippet}
            htmlContent={email.htmlContent}
            originalContent={email.originalContent}
          />

          <div className="flex flex-wrap gap-1 mt-2">
            {/* Show urgency badge if category is Urgent or priority is high */}
            <UrgencyBadge
              priority={email.priority || ''}
              isUrgent={categories.includes('Urgent')}
            />

            {/* Use our memoized CategoryBadges component */}
            <CategoryBadges categories={categories} />
          </div>
        </div>
      </div>
    </div>
  );
};

// Helper to normalise read status across differing property names
const getReadStatus = (email: Email): boolean => {
  if ('read' in email && typeof email.read === 'boolean') {
    return email.read;
  }
  if ('isRead' in email && typeof (email as { isRead?: boolean }).isRead === 'boolean') {
    return (email as { isRead: boolean }).isRead;
  }
  return false;
};

// Use memo with an improved comparison function to prevent unnecessary re-renders
export default memo(EmailItem, (prevProps, nextProps) => {
  // Immutable equality shortcut
  if (prevProps === nextProps) return true;

  const prevEmail = prevProps.email;
  const nextEmail = nextProps.email;

  // Fast exits for primary toggles
  if (prevProps.isSelected !== nextProps.isSelected) return false;

  // Compare stable scalar fields
  if (
    prevEmail.id !== nextEmail.id ||
    prevEmail.subject !== nextEmail.subject ||
    prevEmail.sender !== nextEmail.sender ||
    prevEmail.receivedAt !== nextEmail.receivedAt ||
    prevEmail.summary !== nextEmail.summary ||
    prevEmail.priority !== nextEmail.priority
  ) {
    return false;
  }

  // Compare categories shallowly
  const prevCategories = Array.isArray(prevEmail.categories)
    ? prevEmail.categories
    : [];
  const nextCategories = Array.isArray(nextEmail.categories)
    ? nextEmail.categories
    : [];
  if (prevCategories.length !== nextCategories.length) return false;
  for (let i = 0; i < prevCategories.length; i++) {
    if (prevCategories[i] !== nextCategories[i]) return false;
  }

  // Compare read status
  if (getReadStatus(prevEmail) !== getReadStatus(nextEmail)) return false;

  return true;
});
