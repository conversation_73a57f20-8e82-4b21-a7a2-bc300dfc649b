import { Router, type Request, type Response } from 'express';
import logger from '../lib/logger';
import { ErrorCode, sendError, sendSuccess } from '../lib/standardizedResponses';
import { catchAsync } from '../utils/errorHandler';
import { unifiedEmailProcessing } from '../services/gemini';
import { storage } from '../storage';

const router = Router();

/**
 * Regenerate the AI-generated summary for a single email.
 * POST /api/ai/regenerate-summary/:emailId
 *
 * Response: { summary: string }
 */
router.post(
  '/regenerate-summary/:emailId',
  catchAsync(async (req: Request, res: Response) => {
    const timer = logger.startTimer();
    const operationId = `regenerate-${Date.now()}-${Math.random().toString(36).substring(2, 8)}`;
    const userId = req.user!.id;
    const emailId = Number.parseInt(req.params.emailId, 10);

    logger.ai.operationStart('regenerate-summary', operationId, {
      userId,
      emailId,
      endpoint: '/api/ai/regenerate-summary'
    });

    if (Number.isNaN(emailId)) {
      const duration = timer();
      const error = new Error('Invalid email ID');
      logger.ai.operationFail('regenerate-summary', operationId, error, duration, {
        userId,
        emailId: req.params.emailId,
        reason: 'invalid_email_id'
      });
      return sendError(res, ErrorCode.VALIDATION_ERROR, 'Invalid email ID');
    }

    const email = await storage.getEmail(emailId);

    if (!email) {
      const duration = timer();
      const error = new Error('Email not found');
      logger.ai.operationFail('regenerate-summary', operationId, error, duration, {
        userId,
        emailId,
        reason: 'email_not_found'
      });
      return sendError(res, ErrorCode.NOT_FOUND, 'Email not found');
    }

    // Verify ownership
    if (email.userId !== userId) {
      const duration = timer();
      const error = new Error('Unauthorized access to email');
      logger.ai.operationFail('regenerate-summary', operationId, error, duration, {
        userId,
        emailId,
        emailUserId: email.userId,
        reason: 'unauthorized_access'
      });
      return sendError(res, ErrorCode.FORBIDDEN, 'You do not have permission to access this email');
    }

    logger.ai.info('Processing email regeneration request', {
      operationId,
      userId,
      emailId,
      subject: email.subject?.substring(0, 50) + '...',
      contentLength: email.originalContent?.length || 0
    });

    try {
      // Use the unified processing function
      const aiResult = await unifiedEmailProcessing(
        email.originalContent || '',
        email.subject || '',
        email.sender || 'Unknown'
      );

      // Update the email with new AI data
      await storage.updateEmail(emailId, {
        summary: aiResult.summary,
        categories: aiResult.categories as any,
        aiReply: aiResult.reply,
      });

      const duration = timer();
      logger.ai.operationComplete('regenerate-summary', operationId, duration, {
        userId,
        emailId,
        summaryLength: aiResult.summary.length,
        categoriesCount: aiResult.categories.length,
        replyLength: aiResult.reply.length,
        hasError: !!aiResult.error
      });

      logger.email.info('Successfully regenerated email summary', emailId, userId, {
        operationId,
        summary: aiResult.summary.substring(0, 50) + '...',
        categories: aiResult.categories
      });

      return sendSuccess(res, {
        summary: aiResult.summary,
        categories: aiResult.categories,
        reply: aiResult.reply,
        ...(aiResult.error && { error: aiResult.error }),
      });
    } catch (error) {
      const duration = timer();
      logger.ai.operationFail('regenerate-summary', operationId, error, duration, {
        userId,
        emailId,
        errorType: error instanceof Error ? error.name : 'unknown',
        subject: email.subject?.substring(0, 50) + '...'
      });

      logger.email.error('Failed to regenerate summary', error, emailId, userId, {
        operationId,
        subject: email.subject,
        contentLength: email.originalContent?.length || 0
      });

      return sendError(res, ErrorCode.INTERNAL_ERROR, 'Failed to regenerate summary');
    }
  })
);

export default router; 