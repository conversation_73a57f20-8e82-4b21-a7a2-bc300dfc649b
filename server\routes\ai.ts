import { Router, type Request, type Response } from 'express';
import logger from '../lib/logger';
import { ErrorCode, sendError, sendSuccess } from '../lib/standardizedResponses';
import { catchAsync } from '../utils/errorHandler';
import { unifiedEmailProcessing, generateReplyWithGemini, explainCategorizationWithGemini } from '../services/gemini';
import { storage } from '../storage';

const router = Router();

/**
 * Regenerate the AI-generated summary for a single email.
 * POST /api/ai/regenerate-summary/:emailId
 *
 * Response: { summary: string }
 */
router.post(
  '/regenerate-summary/:emailId',
  catchAsync(async (req: Request, res: Response) => {
    const timer = logger.startTimer();
    const operationId = `regenerate-${Date.now()}-${Math.random().toString(36).substring(2, 8)}`;
    const userId = req.user!.id;
    const emailId = Number.parseInt(req.params.emailId, 10);

    logger.ai.operationStart('regenerate-summary', operationId, {
      userId,
      emailId,
      endpoint: '/api/ai/regenerate-summary'
    });

    if (Number.isNaN(emailId)) {
      const duration = timer();
      const error = new Error('Invalid email ID');
      logger.ai.operationFail('regenerate-summary', operationId, error, duration, {
        userId,
        emailId: req.params.emailId,
        reason: 'invalid_email_id'
      });
      return sendError(res, ErrorCode.VALIDATION_ERROR, 'Invalid email ID');
    }

    const email = await storage.getEmail(emailId);

    if (!email) {
      const duration = timer();
      const error = new Error('Email not found');
      logger.ai.operationFail('regenerate-summary', operationId, error, duration, {
        userId,
        emailId,
        reason: 'email_not_found'
      });
      return sendError(res, ErrorCode.NOT_FOUND, 'Email not found');
    }

    // Verify ownership
    if (email.userId !== userId) {
      const duration = timer();
      const error = new Error('Unauthorized access to email');
      logger.ai.operationFail('regenerate-summary', operationId, error, duration, {
        userId,
        emailId,
        emailUserId: email.userId,
        reason: 'unauthorized_access'
      });
      return sendError(res, ErrorCode.FORBIDDEN, 'You do not have permission to access this email');
    }

    logger.ai.info('Processing email regeneration request', {
      operationId,
      userId,
      emailId,
      subject: email.subject?.substring(0, 50) + '...',
      contentLength: email.originalContent?.length || 0
    });

    try {
      // Use the unified processing function
      const aiResult = await unifiedEmailProcessing(
        email.originalContent || '',
        email.subject || '',
        email.sender || 'Unknown'
      );

      // Update the email with new AI data
      await storage.updateEmail(emailId, {
        summary: aiResult.summary,
        categories: aiResult.categories as any,
        aiReply: aiResult.reply,
      });

      const duration = timer();
      logger.ai.operationComplete('regenerate-summary', operationId, duration, {
        userId,
        emailId,
        summaryLength: aiResult.summary.length,
        categoriesCount: aiResult.categories.length,
        replyLength: aiResult.reply.length,
        hasError: !!aiResult.error
      });

      logger.email.info('Successfully regenerated email summary', emailId, userId, {
        operationId,
        summary: aiResult.summary.substring(0, 50) + '...',
        categories: aiResult.categories
      });

      return sendSuccess(res, {
        summary: aiResult.summary,
        categories: aiResult.categories,
        reply: aiResult.reply,
        ...(aiResult.error && { error: aiResult.error }),
      });
    } catch (error) {
      const duration = timer();
      logger.ai.operationFail('regenerate-summary', operationId, error, duration, {
        userId,
        emailId,
        errorType: error instanceof Error ? error.name : 'unknown',
        subject: email.subject?.substring(0, 50) + '...'
      });

      logger.email.error('Failed to regenerate summary', error, emailId, userId, {
        operationId,
        subject: email.subject,
        contentLength: email.originalContent?.length || 0
      });

      return sendError(res, ErrorCode.INTERNAL_ERROR, 'Failed to regenerate summary');
    }
  })
);

/**
 * Generate an AI reply for a single email.
 * POST /api/ai/generate-reply/:emailId
 *
 * Response: { reply: string, fromCache: boolean }
 */
router.post(
  '/generate-reply/:emailId',
  catchAsync(async (req: Request, res: Response) => {
    const timer = logger.startTimer();
    const operationId = `generate-reply-${Date.now()}-${Math.random().toString(36).substring(2, 8)}`;
    const userId = req.user!.id;
    const emailId = Number.parseInt(req.params.emailId, 10);

    logger.ai.operationStart('generate-reply', operationId, {
      userId,
      emailId,
      endpoint: '/api/ai/generate-reply'
    });

    if (Number.isNaN(emailId)) {
      const duration = timer();
      const error = new Error('Invalid email ID');
      logger.ai.operationFail('generate-reply', operationId, error, duration, {
        userId,
        emailId: req.params.emailId,
        reason: 'invalid_email_id'
      });
      return sendError(res, ErrorCode.VALIDATION_ERROR, 'Invalid email ID');
    }

    const email = await storage.getEmail(emailId);

    if (!email) {
      const duration = timer();
      const error = new Error('Email not found');
      logger.ai.operationFail('generate-reply', operationId, error, duration, {
        userId,
        emailId,
        reason: 'email_not_found'
      });
      return sendError(res, ErrorCode.NOT_FOUND, 'Email not found');
    }

    // Verify ownership
    if (email.userId !== userId) {
      const duration = timer();
      const error = new Error('Unauthorized access to email');
      logger.ai.operationFail('generate-reply', operationId, error, duration, {
        userId,
        emailId,
        emailUserId: email.userId,
        reason: 'unauthorized_access'
      });
      return sendError(res, ErrorCode.FORBIDDEN, 'You do not have permission to access this email');
    }

    // Check if reply already exists
    if (email.aiReply && email.aiReply.trim() !== '') {
      const duration = timer();
      logger.ai.operationComplete('generate-reply', operationId, duration, {
        userId,
        emailId,
        fromCache: true,
        replyLength: email.aiReply.length
      });
      return sendSuccess(res, { reply: email.aiReply, fromCache: true });
    }

    logger.ai.info('Generating new AI reply', {
      operationId,
      userId,
      emailId,
      subject: email.subject?.substring(0, 50) + '...',
      contentLength: email.originalContent?.length || 0
    });

    try {
      // Generate new reply
      const reply = await generateReplyWithGemini(
        email.originalContent || '',
        email.subject || '',
        email.sender || 'Unknown'
      );

      // Update the email with the new reply
      await storage.updateEmail(emailId, { aiReply: reply });

      const duration = timer();
      logger.ai.operationComplete('generate-reply', operationId, duration, {
        userId,
        emailId,
        fromCache: false,
        replyLength: reply.length
      });

      return sendSuccess(res, { reply, fromCache: false });
    } catch (error) {
      const duration = timer();
      logger.ai.operationFail('generate-reply', operationId, error, duration, {
        userId,
        emailId,
        errorType: error instanceof Error ? error.name : 'unknown',
        subject: email.subject?.substring(0, 50) + '...'
      });

      logger.email.error('Failed to generate reply', error, emailId, userId, {
        operationId,
        subject: email.subject,
        contentLength: email.originalContent?.length || 0
      });

      return sendError(res, ErrorCode.INTERNAL_ERROR, 'Failed to generate AI reply');
    }
  })
);

/**
 * Regenerate an AI reply for a single email with a specific tone.
 * POST /api/ai/regenerate-reply/:emailId
 *
 * Body: { tone: string }
 * Response: string (the new reply)
 */
router.post(
  '/regenerate-reply/:emailId',
  catchAsync(async (req: Request, res: Response) => {
    const timer = logger.startTimer();
    const operationId = `regenerate-reply-${Date.now()}-${Math.random().toString(36).substring(2, 8)}`;
    const userId = req.user!.id;
    const emailId = Number.parseInt(req.params.emailId, 10);
    const { tone = 'professional' } = req.body;

    logger.ai.operationStart('regenerate-reply', operationId, {
      userId,
      emailId,
      tone,
      endpoint: '/api/ai/regenerate-reply'
    });

    if (Number.isNaN(emailId)) {
      const duration = timer();
      const error = new Error('Invalid email ID');
      logger.ai.operationFail('regenerate-reply', operationId, error, duration, {
        userId,
        emailId: req.params.emailId,
        reason: 'invalid_email_id'
      });
      return sendError(res, ErrorCode.VALIDATION_ERROR, 'Invalid email ID');
    }

    const email = await storage.getEmail(emailId);

    if (!email) {
      const duration = timer();
      const error = new Error('Email not found');
      logger.ai.operationFail('regenerate-reply', operationId, error, duration, {
        userId,
        emailId,
        reason: 'email_not_found'
      });
      return sendError(res, ErrorCode.NOT_FOUND, 'Email not found');
    }

    // Verify ownership
    if (email.userId !== userId) {
      const duration = timer();
      const error = new Error('Unauthorized access to email');
      logger.ai.operationFail('regenerate-reply', operationId, error, duration, {
        userId,
        emailId,
        emailUserId: email.userId,
        reason: 'unauthorized_access'
      });
      return sendError(res, ErrorCode.FORBIDDEN, 'You do not have permission to access this email');
    }

    logger.ai.info('Regenerating AI reply with tone', {
      operationId,
      userId,
      emailId,
      tone,
      subject: email.subject?.substring(0, 50) + '...',
      contentLength: email.originalContent?.length || 0
    });

    try {
      // Generate new reply with specified tone
      const reply = await generateReplyWithGemini(
        email.originalContent || '',
        email.subject || '',
        email.sender || 'Unknown',
        tone
      );

      // Update the email with the new reply
      await storage.updateEmail(emailId, { aiReply: reply });

      const duration = timer();
      logger.ai.operationComplete('regenerate-reply', operationId, duration, {
        userId,
        emailId,
        tone,
        replyLength: reply.length
      });

      return sendSuccess(res, reply);
    } catch (error) {
      const duration = timer();
      logger.ai.operationFail('regenerate-reply', operationId, error, duration, {
        userId,
        emailId,
        tone,
        errorType: error instanceof Error ? error.name : 'unknown',
        subject: email.subject?.substring(0, 50) + '...'
      });

      logger.email.error('Failed to regenerate reply', error, emailId, userId, {
        operationId,
        tone,
        subject: email.subject,
        contentLength: email.originalContent?.length || 0
      });

      return sendError(res, ErrorCode.INTERNAL_ERROR, 'Failed to regenerate AI reply');
    }
  })
);

/**
 * Explain why an email was categorized in a certain way.
 * POST /api/ai/explain-categories/:emailId
 *
 * Response: string (explanation)
 */
router.post(
  '/explain-categories/:emailId',
  catchAsync(async (req: Request, res: Response) => {
    const timer = logger.startTimer();
    const operationId = `explain-categories-${Date.now()}-${Math.random().toString(36).substring(2, 8)}`;
    const userId = req.user!.id;
    const emailId = Number.parseInt(req.params.emailId, 10);

    logger.ai.operationStart('explain-categories', operationId, {
      userId,
      emailId,
      endpoint: '/api/ai/explain-categories'
    });

    if (Number.isNaN(emailId)) {
      const duration = timer();
      const error = new Error('Invalid email ID');
      logger.ai.operationFail('explain-categories', operationId, error, duration, {
        userId,
        emailId: req.params.emailId,
        reason: 'invalid_email_id'
      });
      return sendError(res, ErrorCode.VALIDATION_ERROR, 'Invalid email ID');
    }

    const email = await storage.getEmail(emailId);

    if (!email) {
      const duration = timer();
      const error = new Error('Email not found');
      logger.ai.operationFail('explain-categories', operationId, error, duration, {
        userId,
        emailId,
        reason: 'email_not_found'
      });
      return sendError(res, ErrorCode.NOT_FOUND, 'Email not found');
    }

    // Verify ownership
    if (email.userId !== userId) {
      const duration = timer();
      const error = new Error('Unauthorized access to email');
      logger.ai.operationFail('explain-categories', operationId, error, duration, {
        userId,
        emailId,
        emailUserId: email.userId,
        reason: 'unauthorized_access'
      });
      return sendError(res, ErrorCode.FORBIDDEN, 'You do not have permission to access this email');
    }

    logger.ai.info('Explaining email categorization', {
      operationId,
      userId,
      emailId,
      categories: email.categories,
      subject: email.subject?.substring(0, 50) + '...',
      contentLength: email.originalContent?.length || 0
    });

    try {
      // Generate explanation for the categorization
      const explanation = await explainCategorizationWithGemini(
        email.originalContent || '',
        email.subject || '',
        email.categories || []
      );

      const duration = timer();
      logger.ai.operationComplete('explain-categories', operationId, duration, {
        userId,
        emailId,
        categories: email.categories,
        explanationLength: explanation.length
      });

      return sendSuccess(res, explanation);
    } catch (error) {
      const duration = timer();
      logger.ai.operationFail('explain-categories', operationId, error, duration, {
        userId,
        emailId,
        categories: email.categories,
        errorType: error instanceof Error ? error.name : 'unknown',
        subject: email.subject?.substring(0, 50) + '...'
      });

      logger.email.error('Failed to explain categories', error, emailId, userId, {
        operationId,
        categories: email.categories,
        subject: email.subject,
        contentLength: email.originalContent?.length || 0
      });

      return sendError(res, ErrorCode.INTERNAL_ERROR, 'Failed to explain email categorization');
    }
  })
);

export default router;