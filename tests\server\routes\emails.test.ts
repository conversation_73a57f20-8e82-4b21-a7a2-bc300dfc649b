/**
 * Emails Route integration-ish tests (router mounted in isolated Express app).
 */

import express from 'express';
import request from 'supertest';

// ---------------------------
// Mock deps
// ---------------------------
const mockGetEmails = jest.fn();
const mockGetEmailById = jest.fn();
const mockUpdateEmailState = jest.fn();

jest.mock('@server/services/email-v2', () => ({
  __esModule: true,
  emailService: {
    getEmails: (...args: any[]) => mockGetEmails(...args),
    getEmailById: (...args: any[]) => mockGetEmailById(...args),
    updateEmailState: (...args: any[]) => mockUpdateEmailState(...args),
  },
}));

// Mock requireAuth to inject test user
jest.mock('@server/middleware/simpleAuth', () => ({
  __esModule: true,
  requireAuth: (_req: any, _res: any, next: any) => {
    // attach fake user
    _req.user = { id: 99, email: '<EMAIL>' };
    return next();
  },
}));

// Silence logger
jest.mock('@server/lib/logger', () => ({
  __esModule: true,
  default: { info: jest.fn(), warn: jest.fn(), error: jest.fn(), debug: jest.fn() },
}));

// Import router after mocks
import emailsRouter from '@server/routes/emails';

describe('Emails API routes', () => {
  const buildApp = () => {
    const app = express();
    app.use(express.json());
    app.use('/api/emails', emailsRouter);
    // error handler fallthrough—standardizedResponses adds handler but okay.
    return app;
  };

  beforeEach(() => jest.clearAllMocks());

  it('GET /api/emails returns paginated list', async () => {
    mockGetEmails.mockResolvedValue({ emails: [{ id: 1 }], pagination: { page: 1 } });

    const res = await request(buildApp()).get('/api/emails?limit=10&offset=0');
    expect(res.status).toBe(200);
    expect(mockGetEmails).toHaveBeenCalledWith(99, {
      page: 1,
      limit: 10,
      filters: { archived: undefined, important: undefined, snoozed: undefined, trashed: undefined },
    });
    expect(res.body.success).toBe(true);
    expect(res.body.data.emails.length).toBe(1);
  });

  it('GET /api/emails with trashed=true returns only trashed emails', async () => {
    mockGetEmails.mockResolvedValue({ emails: [{ id: 2, isTrashed: true }], pagination: { page: 1 } });

    const res = await request(buildApp()).get('/api/emails?trashed=true&limit=10&offset=0');
    expect(res.status).toBe(200);
    expect(mockGetEmails).toHaveBeenCalledWith(99, {
      page: 1,
      limit: 10,
      filters: { archived: undefined, important: undefined, snoozed: undefined, trashed: true },
    });
    expect(res.body.success).toBe(true);
    expect(res.body.data.emails.length).toBe(1);
  });

  it('GET /api/emails/:id validates id and returns 400 for invalid', async () => {
    const res = await request(buildApp()).get('/api/emails/not-a-number');
    expect(res.status).toBe(400);
    expect(res.body.success).toBe(false);
  });

  it('GET /api/emails/:id returns 404 when not found', async () => {
    mockGetEmailById.mockResolvedValue(null);
    const res = await request(buildApp()).get('/api/emails/123');
    expect(res.status).toBe(404);
  });

  it('POST /api/emails/:id/archive archives email', async () => {
    mockUpdateEmailState.mockResolvedValue(true);
    const res = await request(buildApp()).post('/api/emails/5/archive');
    expect(res.status).toBe(200);
    expect(mockUpdateEmailState).toHaveBeenCalledWith(99, 5, { isArchived: true });
  });

  it('POST /api/emails/:id/trash moves email to trash', async () => {
    mockUpdateEmailState.mockResolvedValue(true);
    const res = await request(buildApp()).post('/api/emails/5/trash');
    expect(res.status).toBe(200);
    expect(mockUpdateEmailState).toHaveBeenCalledWith(99, 5, { isTrashed: true });
  });
});
