import { renderHook, waitFor } from '@testing-library/react';
import { useEmailActions } from '@/hooks/use-email-actions';
import apiClient from '@/lib/apiClient';
import { EmailAction } from '@/lib/constants';
import { useToast } from '@/hooks/use-toast';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import type { ReactNode } from 'react';

// Mock dependencies
jest.mock('@/lib/apiClient');
jest.mock('@/hooks/use-toast');
jest.mock('@/hooks/use-permission', () => ({
  usePermission: () => ({
    handlePermissionError: jest.fn().mockResolvedValue(false),
  }),
}));

const mockedApiClient = apiClient as jest.Mocked<typeof apiClient>;
const mockedUseToast = useToast as jest.Mock;
const mockedToast = jest.fn();

// Setup a QueryClient wrapper for the hook
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: { queries: { retry: false } },
  });
  return ({ children }: { children: ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  );
};

describe('useEmailActions', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockedUseToast.mockReturnValue({ toast: mockedToast });
  });

  describe('archiveEmail', () => {
    it('calls the archive endpoint and shows success toast', async () => {
      const emailId = 123;
      mockedApiClient.post.mockResolvedValue({ success: true });
      const wrapper = createWrapper();
      const { result } = renderHook(() => useEmailActions(), { wrapper });

      await result.current.archiveEmail(emailId);

      await waitFor(() => {
        expect(mockedApiClient.post).toHaveBeenCalledWith(`/api/emails/${emailId}/archive`);
        expect(mockedToast).toHaveBeenCalledWith({
          title: 'Success',
          description: 'Email archived successfully.',
        });
      });
    });

    it('shows an error toast when archive fails', async () => {
      const emailId = 456;
      const errorMessage = 'Archive failed';
      mockedApiClient.post.mockRejectedValue(new Error(errorMessage));
      const wrapper = createWrapper();
      const { result } = renderHook(() => useEmailActions(), { wrapper });

      await expect(result.current.archiveEmail(emailId)).rejects.toThrow();

      await waitFor(() => {
        expect(mockedApiClient.post).toHaveBeenCalledWith(`/api/emails/${emailId}/archive`);
        expect(mockedToast).toHaveBeenCalledWith({
          title: `Failed to ${EmailAction.ARCHIVE}`,
          description: errorMessage,
          variant: 'destructive',
        });
      });
    });
  });
}); 