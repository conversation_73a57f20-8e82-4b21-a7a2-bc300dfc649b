-- Merge User Accounts <PERSON><PERSON><PERSON>
-- This script merges user ID 3 (Gmail account) into user ID 1 (MSN account)
-- Run this in your database console (Supabase SQL Editor)

BEGIN;

-- Step 1: Check current state
SELECT 'Current state before merge:' as step;
SELECT id, email, display_name, created_at FROM users WHERE id IN (1, 3);
SELECT user_id, COUNT(*) as email_count FROM emails WHERE user_id IN (1, 3) GROUP BY user_id;

-- Step 2: Update all emails from user 3 to user 1
UPDATE emails 
SET user_id = 1 
WHERE user_id = 3;

-- Step 3: Update any settings/preferences from user 3 to user 1 (if they exist)
UPDATE settings 
SET user_id = 1 
WHERE user_id = 3;

-- Step 4: Update any stats records from user 3 to user 1 (if they exist)
UPDATE stats 
SET user_id = 1 
WHERE user_id = 3;

-- Step 5: Merge Gmail tokens into user 1 if user 3 has them and user 1 doesn't
UPDATE users 
SET 
  gmail_tokens = COALESCE(users.gmail_tokens, u3.gmail_tokens),
  outlook_tokens = COALESCE(users.outlook_tokens, u3.outlook_tokens),
  provider_tokens = COALESCE(users.provider_tokens, u3.provider_tokens),
  last_sync = GREATEST(COALESCE(users.last_sync, '1970-01-01'::timestamp), COALESCE(u3.last_sync, '1970-01-01'::timestamp))
FROM (SELECT * FROM users WHERE id = 3) u3
WHERE users.id = 1;

-- Step 6: Delete the duplicate user record
DELETE FROM users WHERE id = 3;

-- Step 7: Verify the merge
SELECT 'Final state after merge:' as step;
SELECT id, email, display_name, created_at FROM users WHERE id = 1;
SELECT user_id, COUNT(*) as email_count FROM emails WHERE user_id = 1;

COMMIT;

-- If something goes wrong, you can rollback with:
-- ROLLBACK;
