import { useCallback, useEffect, useState } from 'react';
import { useLocation } from 'wouter';
import { useEmailDetail } from '@/context/EmailDetailContext';
import { useEmailList } from '@/context/EmailListContext';
import { useEmailActions } from '@/hooks/use-email-actions';

export interface KeyboardShortcut {
  key: string;
  description: string;
  action: () => void;
  global?: boolean;
}

export const useKeyboardShortcuts = (showHelpModal: () => void) => {
  const [location, navigate] = useLocation();

  // Get email list data from EmailListContext
  const emailList = useEmailList();
  const { emails, filters, setFilters, refreshEmails } = emailList;

  // Get email detail data from EmailDetailContext
  const emailDetail = useEmailDetail();
  const { selectedEmail, selectEmail } = emailDetail;

  // Get email actions for archive functionality
  const { archiveEmail } = useEmailActions();

  const [allShortcuts, setAllShortcuts] = useState<KeyboardShortcut[]>([]);

  // Define shortcuts based on current page - using useCallback to prevent unnecessary re-creation
  const createShortcuts = useCallback(() => {
    // Global shortcuts available on all pages
    const globalShortcuts: KeyboardShortcut[] = [
      {
        key: '?',
        description: 'Show keyboard shortcuts',
        action: showHelpModal,
        global: true,
      },
      {
        key: 'r',
        description: 'Refresh emails',
        action: refreshEmails,
        global: true,
      },
      {
        key: 'g d',
        description: 'Go to Dashboard',
        action: () => navigate('/'),
        global: true,
      },
      {
        key: 'g b',
        description: 'Go to Batch mode',
        action: () => navigate('/batch'),
        global: true,
      },
      {
        key: 'g h',
        description: 'Go to Heatmap',
        action: () => navigate('/heatmap'),
        global: true,
      },
      {
        key: 'g s',
        description: 'Go to Settings',
        action: () => navigate('/settings'),
        global: true,
      },
      {
        key: 'g a',
        description: 'Go to Achievements',
        action: () => navigate('/achievements'),
        global: true,
      },
      {
        key: 'g m',
        description: 'Go to Daily digest',
        action: () => navigate('/digest'),
        global: true,
      },
    ];

    // Page-specific shortcuts
    let pageShortcuts: KeyboardShortcut[] = [];

    // Dashboard-specific shortcuts
    if (location === '/' || location === '/batch') {
      pageShortcuts = [
        {
          key: 'j',
          description: 'Next email',
          action: () => {
            if (!emails || emails.length === 0) return;

            const currentIndex = selectedEmail
              ? emails.findIndex((e) => e.id === selectedEmail.id)
              : -1;

            if (currentIndex < emails.length - 1) {
              selectEmail(emails[currentIndex + 1].id);
            }
          },
        },
        {
          key: 'k',
          description: 'Previous email',
          action: () => {
            if (!emails || emails.length === 0) return;

            const currentIndex = selectedEmail
              ? emails.findIndex((e) => e.id === selectedEmail.id)
              : -1;

            if (currentIndex > 0) {
              selectEmail(emails[currentIndex - 1].id);
            }
          },
        },
        {
          key: 'e',
          description: 'Archive email',
          action: async () => {
            if (selectedEmail) {
              try {
                await archiveEmail(selectedEmail.id);
                // After archiving, select the next email or clear selection
                if (emails && emails.length > 1) {
                  const currentIndex = emails.findIndex((e) => e.id === selectedEmail.id);
                  if (currentIndex < emails.length - 1) {
                    selectEmail(emails[currentIndex + 1].id);
                  } else if (currentIndex > 0) {
                    selectEmail(emails[currentIndex - 1].id);
                  } else {
                    selectEmail(null);
                  }
                } else {
                  selectEmail(null);
                }
              } catch (error) {
                console.error('Failed to archive email:', error);
              }
            }
          },
        },
        {
          key: '/',
          description: 'Search emails',
          action: () => {
            const searchInput = document.querySelector('input[type="search"]') as HTMLInputElement;
            if (searchInput) {
              searchInput.focus();
            }
          },
        },
        {
          key: 'u',
          description: 'Toggle unread filter',
          action: () => {
            setFilters({
              ...filters,
              status: filters.status === 'unread' ? 'all' : 'unread',
            });
          },
        },
        {
          key: 'c',
          description: 'Clear all filters',
          action: () => {
            setFilters({
              categories: [],
              status: 'all',
              priority: 'all',
              timeRange: 'all',
            });
          },
        },
      ];
    }

    // Batch mode specific shortcuts
    if (location === '/batch') {
      pageShortcuts = [
        ...pageShortcuts,
        {
          key: 'a',
          description: 'Select all visible emails',
          action: () => {
            // This would require batch selection state management
            // For now, we'll focus search to select all
            const searchInput = document.querySelector('input[type="search"]') as HTMLInputElement;
            if (searchInput) {
              searchInput.focus();
              searchInput.select();
            }
          },
        },
        {
          key: 'x',
          description: 'Deselect all emails',
          action: () => {
            // Clear any selection by removing focus
            selectEmail(null);
          },
        },
      ];
    }

    // Settings specific shortcuts
    if (location === '/settings') {
      pageShortcuts = [
        {
          key: 's',
          description: 'Save settings',
          action: () => {
            const saveButton = document.querySelector('button[type="submit"]') as HTMLButtonElement;
            if (saveButton) {
              saveButton.click();
            }
          },
        },
      ];
    }

    return [...globalShortcuts, ...pageShortcuts];
  }, [
    location,
    showHelpModal,
    refreshEmails,
    navigate,
    emails,
    selectedEmail,
    selectEmail,
    filters,
    setFilters,
    archiveEmail,
  ]);

  // Update shortcuts when dependencies change
  useEffect(() => {
    setAllShortcuts(createShortcuts());
  }, [createShortcuts]);

  // Handle keyboard events
  const handleKeyDown = useCallback(
    (e: KeyboardEvent) => {
      // Don't trigger shortcuts when typing in input fields
      if (
        e.target instanceof HTMLInputElement ||
        e.target instanceof HTMLTextAreaElement ||
        e.target instanceof HTMLSelectElement ||
        (e.target as HTMLElement).isContentEditable
      ) {
        return;
      }

      // Handle multi-key shortcuts (like 'g d')
      if (e.key === 'g') {
        const handleSecondKey = (secondEvent: KeyboardEvent) => {
          const combinedKey = `g ${secondEvent.key}`;
          const shortcut = allShortcuts.find((s) => s.key === combinedKey);

          if (shortcut) {
            secondEvent.preventDefault();
            shortcut.action();
          }

          // Clean up event listener after handling or timeout
          window.removeEventListener('keydown', handleSecondKey);
        };

        window.addEventListener('keydown', handleSecondKey);
        // Clean up listener after 1 second in case second key never comes
        setTimeout(() => {
          window.removeEventListener('keydown', handleSecondKey);
        }, 1000);

        return;
      }

      // Handle single key shortcuts
      const shortcut = allShortcuts.find((s) => s.key === e.key);
      if (shortcut) {
        e.preventDefault();
        shortcut.action();
      }
    },
    [allShortcuts]
  );

  // Attach and clean up event listener
  useEffect(() => {
    window.addEventListener('keydown', handleKeyDown);
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [handleKeyDown]);

  return { shortcuts: allShortcuts };
};
